"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[675],{60675:function(a,t,e){e.r(t),e.d(t,{default:function(){return d}});var r=function(){var a=this,t=a._self._c;return t("page-header-wrapper",[t("a-card",{attrs:{bordered:!1}},[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"table-page-search-wrapper"},[t("a-form",{attrs:{layout:"inline"}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户Id"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:a.queryParam.userId,callback:function(t){a.$set(a.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"真实姓名"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入真实姓名"},model:{value:a.queryParam.realName,callback:function(t){a.$set(a.queryParam,"realName",t)},expression:"queryParam.realName"}})],1)],1),t("a-col",{attrs:{md:12,lg:8,sm:24}},[t("a-form-item",[t("span",{staticClass:"table-page-search-submitButtons"},[t("a-button",{attrs:{icon:"redo"},on:{click:a.getqueryParam}},[a._v(" 重置")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){a.queryParam.pageNum=1,a.getlist()}}},[a._v("查询 ")])],1)])],1)],1)],1)],1)]),t("a-table",{attrs:{bordered:"",loading:a.loading,pagination:a.pagination,columns:a.columns,"data-source":a.datalist,rowKey:"id"},scopedSlots:a._u([{key:"amtFrom",fn:function(e,r){return t("span",{},[[t("a-tag",{attrs:{color:"融资"==r.amtFrom?"blue":"指数"==r.amtFrom?"purple":"orange"}},[a._v(a._s(r.amtFrom))])]],2)}},{key:"amtTo",fn:function(e,r){return t("span",{},[[t("a-tag",{attrs:{color:"融资"==r.amtTo?"blue":"指数"==r.amtTo?"purple":"orange"}},[a._v(a._s(r.amtTo))])]],2)}}])})],1)],1)},n=[],o=(e(27609),e(71008)),i=e(46279),s=e.n(i),l={name:"fundTransferrecord",data:function(){var a=this;return{columns:[{title:"用户名称（ID）",dataIndex:"realName",align:"center",width:180,customRender:function(a,t,e){return"".concat(t.realName,"（").concat(t.userId,"）")}},{title:"代理ID",dataIndex:"agentId",align:"center"},{title:"转出账户",dataIndex:"amtFrom",align:"center",scopedSlots:{customRender:"amtFrom"}},{title:"转出金额",dataIndex:"transAmt",align:"center"},{title:"转入账户",dataIndex:"amtTo",align:"center",scopedSlots:{customRender:"amtTo"}},{title:"创建时间",dataIndex:"addTime",align:"center",width:180,customRender:function(a,t,e){return a?s()(a).format("YYYY-MM-DD HH:mm:ss"):""}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,e){return a.onSizeChange(t,e)},onChange:function(t,e){return a.onPageChange(t,e)},showTotal:function(a){return"共有 ".concat(a," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,userId:"",realName:""},datalist:[],agentlist:[],agentloading:!1,agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){this.getlist()},methods:{getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,userId:"",realName:""}},getlist:function(){var a=this,t=this;this.loading=!0,(0,o.Be)(this.queryParam).then((function(e){a.datalist=e.data.list,a.pagination.total=e.data.total,setTimeout((function(){t.loading=!1}),500)}))},onPageChange:function(a,t){this.queryParam.pageNum=a,this.getlist()},onSizeChange:function(a,t){this.queryParam.pageNum=a,this.queryParam.pageSize=t,this.getlist()}}},u=l,m=e(6367),c=(0,m.A)(u,r,n,!1,null,"7e64d830",null),d=c.exports}}]);