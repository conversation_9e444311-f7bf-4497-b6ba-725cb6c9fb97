import request from '@/utils/request'
import qs from 'qs'
const userApi = {
  rechargelist: '/admin/recharge/list.do', // 充值列表
  rechargedel: '/admin/recharge/del.do', // 充值列表删除
  rechargeupdateState: '/admin/recharge/updateState.do', // 充值列表修改状态
  rechargecreateOrder: '/admin/recharge/createOrder.do', // 新增充值订单
  rechargeexport: '/admin/recharge/export.do', // 充值订单导出
  rechargeCountRechargeAmount: '/admin/recharge/countRechargeAmount.do', // 充值统计
  withdrawCountRechargeAmount: '/admin/withdraw/countRechargeAmount.do', // 提现统计
  withdrawlist: '/admin/withdraw/list.do', // 提现列表
  withdrawupdateState: 'admin/withdraw/updateState.do', // 提现列表修改状态
  withdrawexport: '/admin/withdraw/export.do', // 提现列表导出
  cashlist: '/admin/cash/list.do', // 资金记录
  logtransList: '/admin/log/transList.do', // 资金互转记录
  getPendingOrderNum: '/admin/withdraw/getPendingOrderNum.do',
  getOrderNum: '/admin/withdraw/getRealPendingOrderNum.do', // 资金互转记录
  fundList: '/admin/user/fund/list.do',
  getWithdrawableAmount: '/admin/user/fund/withdrawable.do',
  updateUserFund: '/admin/user/fund/update.do', // 修改用户可提现金额
  fundConsumptionList: '/admin/user/fund/consumption/list.do', // 资金消费记录列表
  fundConsumptionBySource: '/admin/user/fund/consumption/byFundSource.do', // 根据资金来源ID查询消费记录
  userFundFlowList: '/admin/user/fund/flow/list.do', // 用户资金流水列表
  queryFundFlowByOrderSn: '/admin/user/fund/flow/queryByOrderSn.do', // 根据关联订单号查询流水记录
  getChangeTypes: '/admin/user/fund/flow/getChangeTypes.do', // 获取所有账变类型
  rechargeStatistics: '/admin/recharge/rechargeStatistics.do', // 充值详细统计
  withdrawStatistics: '/admin/withdraw/withdrawStatistics.do' // 提现详细统计

}

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */

export function rechargelist(parameter) {
  return request({
    url: userApi.rechargelist,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function withdrawStatistics(parameter) {
  return request({
    url: userApi.withdrawStatistics,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function rechargeStatistics(parameter) {
  return request({
    url: userApi.rechargeStatistics,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function rechargedel(parameter) {
  return request({
    url: userApi.rechargedel,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function rechargeupdateState(parameter) {
  return request({
    url: userApi.rechargeupdateState,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function rechargecreateOrder(parameter) {
  return request({
    url: userApi.rechargecreateOrder,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function rechargeexport(parameter) {
  return request({
    url: userApi.rechargeexport,
    method: 'post',
    responseType: 'blob',
    data: qs.stringify(parameter)
  })
}

export function rechargeCountRechargeAmount(parameter) {
  return request({
    url: userApi.rechargeCountRechargeAmount,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function withdrawCountRechargeAmount(parameter) {
  return request({
    url: userApi.withdrawCountRechargeAmount,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function withdrawlist(parameter) {
  return request({
    url: userApi.withdrawlist,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function withdrawupdateState(parameter) {
  return request({
    url: userApi.withdrawupdateState,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function withdrawexport(parameter) {
  return request({
    url: userApi.withdrawexport,
    method: 'post',
    responseType: 'blob',
    data: qs.stringify(parameter)
  })
}

export function getPendingOrderNum(parameter) {
  return request({
    url: userApi.getPendingOrderNum + '?v=' + Date.now(),
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function getOrderNum(parameter) {
  return request({
    url: userApi.getOrderNum + '?v=' + Date.now(),
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function cashlist(parameter) {
  return request({
    url: userApi.cashlist,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function logtransList(parameter) {
  return request({
    url: userApi.logtransList,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function fundList(parameter) {
  return request({
    url: userApi.fundList,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function getWithdrawableAmount(parameter) {
  return request({
    url: userApi.getWithdrawableAmount,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function updateUserFund(parameter) {
  return request({
    url: userApi.updateUserFund,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function fundConsumptionList(parameter) {
  return request({
    url: userApi.fundConsumptionList,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function fundConsumptionBySource(parameter) {
  return request({
    url: userApi.fundConsumptionBySource,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function getUserFundFlowList(parameter) {
  return request({
    url: userApi.userFundFlowList,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function queryFundFlowByOrderSn(parameter) {
  return request({
    url: userApi.queryFundFlowByOrderSn,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function getChangeTypes(parameter) {
  return request({
    url: userApi.getChangeTypes,
    method: 'post',
    data: qs.stringify(parameter)
  })
}
