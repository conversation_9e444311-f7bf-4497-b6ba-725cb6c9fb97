"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[4821],{14821:function(e,a,t){t.r(a),t.d(a,{default:function(){return f}});var r=function(){var e=this,a=e._self._c;return a("page-header-wrapper",[a("a-card",{attrs:{bordered:!1,title:"平台设置"}},[a("div",{attrs:{slot:"extra"},slot:"extra"},[a("a-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:e.handleSave}},[e._v(" 保存设置 ")])],1),a("a-spin",{attrs:{spinning:e.loading}},[a("a-form",{attrs:{form:e.form,"label-col":e.labelCol,"wrapper-col":e.wrapperCol}},[a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{span:24}},[a("a-divider",{attrs:{orientation:"left"}},[e._v("支付设置")])],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:12,lg:12,sm:24}},[a("a-form-item",{attrs:{label:"支付开关"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["payEnabled",{rules:[{required:!0,message:"请选择支付开关状态"}]}],expression:"['payEnabled', { rules: [{ required: true, message: '请选择支付开关状态' }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("a-icon",{staticStyle:{color:"#52c41a"},attrs:{type:"check-circle"}}),e._v(" 开启 ")],1),a("a-radio",{attrs:{value:0}},[a("a-icon",{staticStyle:{color:"#f5222d"},attrs:{type:"close-circle"}}),e._v(" 关闭 ")],1)],1)],1)],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:24,lg:24,sm:24}},[a("a-form-item",{attrs:{label:"支付关闭提示信息"}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["payDisabledMessage",{rules:[{required:!0,message:"请输入支付关闭时的提示信息"},{max:200,message:"提示信息不能超过200个字符"}]}],expression:"['payDisabledMessage', {\n                                rules: [\n                                    { required: true, message: '请输入支付关闭时的提示信息' },\n                                    { max: 200, message: '提示信息不能超过200个字符' }\n                                ]\n                            }]"}],attrs:{placeholder:"请输入支付关闭时的提示信息，当支付开关关闭时，用户将看到此提示信息",rows:4}})],1)],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{span:24}},[a("a-divider",{attrs:{orientation:"left"}},[e._v("充值限制设置")])],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:12,lg:12,sm:24}},[a("a-form-item",{attrs:{label:"充值次数限制开关"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["rechargeLimitEnabled",{rules:[{required:!0,message:"请选择充值次数限制开关状态"}]}],expression:"['rechargeLimitEnabled', { rules: [{ required: true, message: '请选择充值次数限制开关状态' }] }]"}]},[a("a-radio",{attrs:{value:1}},[a("a-icon",{staticStyle:{color:"#52c41a"},attrs:{type:"check-circle"}}),e._v(" 开启 ")],1),a("a-radio",{attrs:{value:0}},[a("a-icon",{staticStyle:{color:"#f5222d"},attrs:{type:"close-circle"}}),e._v(" 关闭 ")],1)],1)],1)],1),a("a-col",{attrs:{md:12,lg:12,sm:24}},[a("a-form-item",{attrs:{label:"每日充值最大笔数"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["rechargeLimitDaily",{rules:[{required:!0,message:"请输入每日充值最大笔数"}]}],expression:"['rechargeLimitDaily', { rules: [{ required: true, message: '请输入每日充值最大笔数' }] }]"}],staticStyle:{width:"100%"},attrs:{min:1,max:999,placeholder:"请输入每日最大充值笔数"}})],1)],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:24,lg:24,sm:24}},[a("a-form-item",{attrs:{label:"充值次数超限提示信息"}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["rechargeLimitMessage",{rules:[{required:!0,message:"请输入充值次数超限时的提示信息"},{max:200,message:"提示信息不能超过200个字符"}]}],expression:"['rechargeLimitMessage', {\n                                rules: [\n                                    { required: true, message: '请输入充值次数超限时的提示信息' },\n                                    { max: 200, message: '提示信息不能超过200个字符' }\n                                ]\n                            }]"}],attrs:{placeholder:"请输入当用户充值次数超过限制时显示的提示信息",rows:4,maxLength:200,showCount:""}})],1)],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{span:24}},[a("a-divider",{attrs:{orientation:"left"}},[e._v("其他设置")])],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:24,lg:24,sm:24}},[a("a-form-item",{attrs:{label:"七牛域名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["qiniuDomain",{}],expression:"['qiniuDomain', {}]"}],attrs:{placeholder:"七牛云存储域名地址",disabled:""}}),a("div",{staticClass:"ant-form-explain"},[a("a-icon",{staticStyle:{color:"#1890ff"},attrs:{type:"info-circle"}}),e._v(" 此项为系统配置，暂不支持修改 ")],1)],1)],1)],1)],1)],1)],1),a("a-card",{staticStyle:{"margin-top":"16px"},attrs:{bordered:!1,title:"操作说明"}},[a("div",{staticClass:"setting-tips"},[a("h4",[a("a-icon",{attrs:{type:"bulb"}}),e._v(" 使用说明：")],1),a("ul",[a("li",[a("strong",[e._v("支付开关：")]),e._v("控制系统整体支付功能的开启和关闭状态")]),a("li",[a("strong",[e._v("支付关闭提示信息：")]),e._v("当支付功能关闭时，向用户显示的提示信息")]),a("li",[a("strong",[e._v("充值次数限制开关：")]),e._v("控制是否启用每日充值次数限制功能")]),a("li",[a("strong",[e._v("每日充值最大笔数：")]),e._v("用户每日可发起的充值订单最大数量（不论状态）")]),a("li",[a("strong",[e._v("充值次数超限提示信息：")]),e._v("当用户超过每日充值次数限制时显示的提示信息")]),a("li",[a("strong",[e._v("七牛域名：")]),e._v("系统文件存储域名，由系统管理员配置")])]),a("a-alert",{staticStyle:{"margin-top":"16px"},attrs:{message:"重要提示",description:"支付开关关闭后，所有充值操作都将被禁用；充值次数限制可以有效防止恶意刷单行为，请合理设置限制数量。",type:"warning","show-icon":""}})],1)])],1)},s=[],i=t(53177),o=t(93251),n=t(16771),l=t(85070),c=t.n(l);function d(){return(0,n.Ay)({url:"admin/system/getInitConfig",method:"post"})}function m(e){return(0,n.Ay)({url:"admin/system/updateConfig",method:"post",data:c().stringify(e)})}var g={name:"PlatformSetting",data:function(){return{form:this.$form.createForm(this),loading:!1,saveLoading:!1,configData:{},labelCol:{xs:{span:24},sm:{span:5},md:{span:5}},wrapperCol:{xs:{span:24},sm:{span:19},md:{span:19}}}},mounted:function(){this.getConfigData()},methods:{getConfigData:function(){var e=this;return(0,o.A)((0,i.A)().m((function a(){var t,r;return(0,i.A)().w((function(a){while(1)switch(a.n){case 0:return e.loading=!0,a.p=1,a.n=2,d();case 2:t=a.v,console.log("获取配置数据响应:",t),0===t.status&&t.success?(e.configData=t.data,e.$nextTick((function(){e.form.setFieldsValue({payEnabled:e.configData.payEnabled,payDisabledMessage:e.configData.payDisabledMessage||"",qiniuDomain:e.configData.qiniuDomain||"",rechargeLimitEnabled:e.configData.rechargeLimitEnabled,rechargeLimitDaily:e.configData.rechargeLimitDaily,rechargeLimitMessage:e.configData.rechargeLimitMessage||""})}))):e.$message.error(t.msg||"获取配置失败"),a.n=4;break;case 3:a.p=3,r=a.v,console.error("获取配置失败:",r),e.$message.error("获取配置失败，请稍后重试");case 4:return a.p=4,e.loading=!1,a.f(4);case 5:return a.a(2)}}),a,null,[[1,3,4,5]])})))()},handleSave:function(){var e=this;this.form.validateFields((function(a,t){a||e.saveConfig(t)}))},saveConfig:function(e){var a=this;return(0,o.A)((0,i.A)().m((function t(){var r,s,o;return(0,i.A)().w((function(t){while(1)switch(t.n){case 0:return a.saveLoading=!0,t.p=1,r={id:a.configData.id,payEnabled:e.payEnabled,payDisabledMessage:e.payDisabledMessage,rechargeLimitEnabled:e.rechargeLimitEnabled,rechargeLimitDaily:e.rechargeLimitDaily,rechargeLimitMessage:e.rechargeLimitMessage},console.log("保存配置参数:",r),t.n=2,m(r);case 2:s=t.v,console.log("保存配置响应:",s),0===s.status&&s.success?(a.$message.success("保存成功"),a.getConfigData()):a.$message.error(s.msg||"保存失败"),t.n=4;break;case 3:t.p=3,o=t.v,console.error("保存配置失败:",o),a.$message.error("保存失败，请稍后重试");case 4:return t.p=4,a.saveLoading=!1,t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()}}},u=g,p=t(6367),v=(0,p.A)(u,r,s,!1,null,"ff4317f2",null),f=v.exports}}]);