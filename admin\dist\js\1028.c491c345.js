(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[1028],{11334:function(e,r,t){"use strict";t.d(r,{FJ:function(){return c},IU:function(){return n},J5:function(){return p},Up:function(){return m},WU:function(){return u},WY:function(){return g},sE:function(){return l},xi:function(){return d},zX:function(){return v}});var a=t(16771),s=t(85070),i=t.n(s),o={getProductSetting:"/api/admin/getProductSetting.do",productupdate:"/admin/product/update.do",admingetSetting:"/api/admin/getSetting.do",setupdate:"/admin/set/update.do",admingetIndexSetting:"/api/admin/getIndexSetting.do",siteindexupdate:"/admin/site/index/update.do",admingetFuturesSetting:"/api/admin/getFuturesSetting.do",sitefuturesupdate:"/admin/site/futures/update.do",admingetSiteSpreadList:"/api/admin/getSiteSpreadList.do",adminaddSiteSpread:"/api/admin/addSiteSpread.do",adminupdateSiteSpread:"/api/admin/updateSiteSpread.do"};function l(e){return(0,a.Ay)({url:o.getProductSetting,method:"post",data:i().stringify(e)})}function d(e){return(0,a.Ay)({url:o.productupdate,method:"post",data:i().stringify(e)})}function u(e){return(0,a.Ay)({url:o.admingetSetting,method:"post",data:i().stringify(e)})}function n(e){return(0,a.Ay)({url:o.setupdate,method:"post",data:i().stringify(e)})}function m(e){return(0,a.Ay)({url:o.admingetIndexSetting,method:"post",data:i().stringify(e)})}function c(e){return(0,a.Ay)({url:o.siteindexupdate,method:"post",data:i().stringify(e)})}function g(e){return(0,a.Ay)({url:o.admingetSiteSpreadList,method:"post",data:i().stringify(e)})}function p(e){return(0,a.Ay)({url:o.adminaddSiteSpread,method:"post",data:i().stringify(e)})}function v(e){return(0,a.Ay)({url:o.adminupdateSiteSpread,method:"post",data:i().stringify(e)})}},21028:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return c}});var a=function(){var e=this,r=e._self._c;return r("page-header-wrapper",[r("a-form",{ref:"addUserform",staticClass:"form",attrs:{form:e.addUserform}},[r("a-card",{staticClass:"card",attrs:{title:"后台设置",bordered:!1,loading:e.loading}},[r("span",{attrs:{slot:"extra"},slot:"extra"}),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"登陆开启白名单"}},[r("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["openWhiteList",{rules:[{required:!0,message:"请选择状态"}]}],expression:"['openWhiteList', { rules: [{ required: true, message: '请选择状态', }] }]"}],attrs:{placeholder:"请选择状态"}},[r("a-select-option",{attrs:{value:0}},[e._v("否")]),r("a-select-option",{attrs:{value:1}},[e._v("是")])],1)],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"充值开始时间（例：09:00）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["rechargeBegin",{rules:[{required:!0,message:"请输入充值开始时间"}]}],expression:"['rechargeBegin', { rules: [{ required: true, message: '请输入充值开始时间', }] }]"}],attrs:{placeholder:"请输入充值开始时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"充值结束时间（例：17:00）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["rechargeEnd",{rules:[{required:!0,message:"请输入充值结束时间"}]}],expression:"['rechargeEnd', { rules: [{ required: true, message: '请输入充值结束时间', }] }]"}],attrs:{placeholder:"请输入充值结束时间"}})],1)],1)],1)],1),r("a-card",{staticClass:"card",attrs:{title:"时间设置",bordered:!1,loading:e.loading}},[r("span",{attrs:{slot:"extra"},slot:"extra"},[e._v('填写规则请按照括号中的示例填写，":"统一为英文中的字符，提现时间为24小时制，请填写整数')]),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"上午开始交易时间（例：9:30）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transAmBegin",{rules:[{required:!0,message:"请输入上午开始交易时间"}]}],expression:"['transAmBegin', { rules: [{ required: true, message: '请输入上午开始交易时间', }] }]"}],attrs:{placeholder:"请输入上午开始交易时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"上午结束交易时间（例：10:30）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transAmEnd",{rules:[{required:!0,message:"请输入上午结束交易时间"}]}],expression:"['transAmEnd', { rules: [{ required: true, message: '请输入上午结束交易时间', }] }]"}],attrs:{placeholder:"请输入上午结束交易时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"下午开始交易时间（例：13:30）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transPmBegin",{rules:[{required:!0,message:"请输入下午开始交易时间"}]}],expression:"['transPmBegin', { rules: [{ required: true, message: '请输入下午开始交易时间', }] }]"}],attrs:{placeholder:"请输入下午开始交易时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"下午结束交易时间（例：15:00）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transPmEnd",{rules:[{required:!0,message:"请输入下午结束交易时间"}]}],expression:"['transPmEnd', { rules: [{ required: true, message: '请输入下午结束交易时间', }] }]"}],attrs:{placeholder:"请输入下午结束交易时间"}})],1)],1)],1),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"提现上午开始时间（例：9:30）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withdrawAmBegin",{rules:[{required:!0,message:"请输入提现上午开始时间"}]}],expression:"['withdrawAmBegin', { rules: [{ required: true, message: '请输入提现上午开始时间', }] }]"}],attrs:{placeholder:"请输入提现上午开始时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"提现上午结束时间（例：10:30）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withdrawAmEnd",{rules:[{required:!0,message:"请输入提现上午结束时间"}]}],expression:"['withdrawAmEnd', { rules: [{ required: true, message: '请输入提现上午结束时间', }] }]"}],attrs:{placeholder:"请输入提现上午结束时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"下提现下午开始时间（例：13:30）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withdrawPmBegin",{rules:[{required:!0,message:"请输入提现下午开始时间"}]}],expression:"['withdrawPmBegin', { rules: [{ required: true, message: '请输入提现下午开始时间', }] }]"}],attrs:{placeholder:"请输入提现下午开始时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"提现下午结束时间（例：15:00）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withdrawPmEnd",{rules:[{required:!0,message:"请输入提现下午结束时间"}]}],expression:"['withdrawPmEnd', { rules: [{ required: true, message: '请输入提现下午结束时间', }] }]"}],attrs:{placeholder:"请输入提现下午结束时间"}})],1)],1)],1)],1),r("a-card",{staticClass:"card",attrs:{title:"费用设置",bordered:!1,loading:e.loading}},[r("span",{attrs:{slot:"extra"},slot:"extra"},[e._v("请按照括号中的示例填写")]),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"买入手续费（例:0.001）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyFee",{rules:[{required:!0,message:"请输入买入手续费"}]}],expression:"['buyFee', { rules: [{ required: true, message: '请输入买入手续费', }] }]"}],attrs:{placeholder:"请输入买入手续费"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"卖出手续费（例:0.001）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["sellFee",{rules:[{required:!0,message:"请输入卖出手续费"}]}],expression:"['sellFee', { rules: [{ required: true, message: '请输入卖出手续费', }] }]"}],attrs:{placeholder:"请输入卖出手续费"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"留仓费（例:0.001）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["stayFee",{rules:[{required:!0,message:"请输入留仓费"}]}],expression:"['stayFee', { rules: [{ required: true, message: '请输入留仓费', }] }]"}],attrs:{placeholder:"请输入留仓费"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"印花税（例:0.001）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["dutyFee",{rules:[{required:!0,message:"请输入印花税"}]}],expression:"['dutyFee', { rules: [{ required: true, message: '请输入印花税', }] }]"}],attrs:{placeholder:"请输入印花税"}})],1)],1)],1)],1),r("a-card",{staticClass:"card",attrs:{title:"购买设置",bordered:!1,loading:e.loading}},[r("span",{attrs:{slot:"extra"},slot:"extra"},[e._v("请按照括号中的示例填写")]),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"最小购买金额（例:1000）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyMinAmt",{rules:[{required:!0,message:"请输入最小购买金额"}]}],expression:"['buyMinAmt', { rules: [{ required: true, message: '请输入最小购买金额', }] }]"}],attrs:{placeholder:"请输入最小购买金额"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"最大买入比例（例:0.8）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyMaxAmtPercent",{rules:[{required:!0,message:"请输入最大买入比例"}]}],expression:"['buyMaxAmtPercent', { rules: [{ required: true, message: '请输入最大买入比例', }] }]"}],attrs:{placeholder:"请输入最大买入比例"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"最小购买股数（例:5000）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyMinNum",{rules:[{required:!0,message:"请输入最小购买股数"}]}],expression:"['buyMinNum', { rules: [{ required: true, message: '请输入最小购买股数', }] }]"}],attrs:{placeholder:"请输入最小购买股数"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"最大买入股数（例:1000000）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyMaxNum",{rules:[{required:!0,message:"请输入最大买入股数"}]}],expression:"['buyMaxNum', { rules: [{ required: true, message: '请输入最大买入股数', }] }]"}],attrs:{placeholder:"请输入最大买入股数"}})],1)],1)],1),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"杠杆倍数（例:100/50/30）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["siteLever",{rules:[{required:!0,message:"请输入杠杆倍数"}]}],expression:"['siteLever', { rules: [{ required: true, message: '请输入杠杆倍数', }] }]"}],attrs:{placeholder:"请输入杠杆倍数"}})],1)],1),r("a-col",{attrs:{md:12,lg:12,sm:12}},[r("a-form-item",{attrs:{label:"买入多长时间内不能平仓/分钟（例:30）"}},[r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["cantSellTimes",{rules:[{required:!0,message:"请输入时间"}]}],expression:"['cantSellTimes', { rules: [{ required: true, message: '请输入时间', }] }]"}],staticStyle:{width:"100%"},attrs:{placeholder:"请输入时间"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"VIP抢筹资金限制"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["vipQcMaxAmt",{rules:[{required:!0,message:"请输入VIP抢筹资金数量"}]}],expression:"['vipQcMaxAmt', { rules: [{ required: true, message: '请输入VIP抢筹资金数量', }] }]"}],attrs:{placeholder:"请输入VIP抢筹资金数量"}})],1)],1)],1),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:24,lg:24,sm:24}},[r("a-form-item",{attrs:{label:"设置多少分钟内同一只股票不得下单多少次(同一用户)"}},[r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["buySameTimes",{rules:[{required:!0,message:"请输入分钟数"}]}],expression:"['buySameTimes', { rules: [{ required: true, message: '请输入分钟数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入分钟数"}}),e._v(" 分钟内同一只股票不得下单 "),r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["buySameNums",{rules:[{required:!0,message:"请输入下单次数"}]}],expression:"['buySameNums', { rules: [{ required: true, message: '请输入下单次数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入下单次数"}}),e._v(" 次 ")],1)],1)],1),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:24,lg:24,sm:24}},[r("a-form-item",{attrs:{label:"设置多少分钟内交易手数不得超过多少手(同一用户)"}},[r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyNumTimes",{rules:[{required:!0,message:"请输入分钟数"}]}],expression:"['buyNumTimes', { rules: [{ required: true, message: '请输入分钟数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入分钟数"}}),e._v(" 分钟内交易手数不得超过 "),r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyNumLots",{rules:[{required:!0,message:"请输入下单手数"}]}],expression:"['buyNumLots', { rules: [{ required: true, message: '请输入下单手数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入下单手数"}}),e._v(" 手 ")],1)],1)],1),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:24,lg:24,sm:24}},[r("a-form-item",{attrs:{label:"同一股票连续 x 天 内涨幅超过 y 不能买入(同一用户)"}},[e._v(" 同一股票连续 "),r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stockDays",{rules:[{required:!0,message:"请输入天数"}]}],expression:"['stockDays', { rules: [{ required: true, message: '请输入天数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入天数"}}),e._v(" 天 内涨幅超过 "),r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["stockRate",{rules:[{required:!0,message:"请输入下单次数"}]}],expression:"['stockRate', { rules: [{ required: true, message: '请输入下单次数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入下单次数"}}),e._v(" 次不能买入(同一用户) ")],1)],1)],1),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"A股超过多少个点不能买入（例:7)"}},[r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["creaseMaxPercent",{rules:[{required:!0,message:"请输入点数"}]}],expression:"['creaseMaxPercent', { rules: [{ required: true, message: '请输入点数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入点数"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"科创板超过多少个点不能买入（例:7)"}},[r("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["kcCreaseMaxPercent",{rules:[{required:!0,message:"请输入点数"}]}],expression:"['kcCreaseMaxPercent', { rules: [{ required: true, message: '请输入点数', }] }]"}],staticStyle:{width:"300px"},attrs:{placeholder:"请输入点数"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"股票买入是否自动成交"}},[r("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["stockAutoDeal",{rules:[{required:!0,message:"请选择状态"}]}],expression:"['stockAutoDeal', { rules: [{ required: true, message: '请选择状态', }] }]"}],attrs:{placeholder:"请选择状态"}},[r("a-select-option",{attrs:{value:0}},[e._v("否")]),r("a-select-option",{attrs:{value:1}},[e._v("是")])],1)],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"大宗交易买入是否自动成交"}},[r("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["dzStockAutoDeal",{rules:[{required:!0,message:"请选择状态"}]}],expression:"['dzStockAutoDeal', { rules: [{ required: true, message: '请选择状态', }] }]"}],attrs:{placeholder:"请选择状态"}},[r("a-select-option",{attrs:{value:0}},[e._v("否")]),r("a-select-option",{attrs:{value:1}},[e._v("是")])],1)],1)],1)],1)],1),r("a-card",{staticClass:"card",attrs:{title:"强制平仓设置",bordered:!1,loading:e.loading}},[r("span",{attrs:{slot:"extra"},slot:"extra"},[e._v("请按照括号中的示例填写,比例均采用小数来表示    强制平仓线计算规则：可用资金 + （冻结保证金 * 强制平仓比例）")]),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"强制平仓比例（例:0.7）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["forceStopPercent",{rules:[{required:!0,message:"请输入强制平仓比例"}]}],expression:"['forceStopPercent', { rules: [{ required: true, message: '请输入强制平仓比例', }] }]"}],attrs:{placeholder:"请输入强制平仓比例"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"连续涨停强制平仓（例:0.2）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["hightAndLow",{rules:[{required:!0,message:"请输入连续涨停强制平仓比例"}]}],expression:"['hightAndLow', { rules: [{ required: true, message: '请输入连续涨停强制平仓比例', }] }]"}],attrs:{placeholder:"请输入连续涨停强制平仓比例"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"最大留仓天数（例:15）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["stayMaxDays",{rules:[{required:!0,message:"请输入最大留仓天数"}]}],expression:"['stayMaxDays', { rules: [{ required: true, message: '请输入最大留仓天数', }] }]"}],attrs:{placeholder:"请输入最大留仓天数"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"强制平仓手续费（例:0.001）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["forceStopFee",{rules:[{required:!0,message:"请输入强制平仓手续费"}]}],expression:"['forceStopFee', { rules: [{ required: true, message: '请输入强制平仓手续费', }] }]"}],attrs:{placeholder:"请输入强制平仓手续费"}})],1)],1)],1),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"强制平仓提醒比例（例:0.5）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["forceStopRemindRatio",{rules:[{required:!0,message:"请输入强制平仓提醒比例"}]}],expression:"['forceStopRemindRatio', { rules: [{ required: true, message: '请输入强制平仓提醒比例', }] }]"}],attrs:{placeholder:"请输入强制平仓提醒比例"}})],1)],1)],1)],1),r("a-card",{staticClass:"card",attrs:{title:"充值提现设置",bordered:!1,loading:e.loading}},[r("span",{attrs:{slot:"extra"},slot:"extra"},[e._v("请按照括号中的示例填写,比例均采用小数来表示")]),r("a-row",{staticClass:"form-row",attrs:{gutter:48}},[r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"最小充值金额（例:1000）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["chargeMinAmt",{rules:[{required:!0,message:"请输入最小充值金额"}]}],expression:"['chargeMinAmt', { rules: [{ required: true, message: '请输入最小充值金额', }] }]"}],attrs:{placeholder:"请输入最小充值金额"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"最小提现金额（例:1000）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withMinAmt",{rules:[{required:!0,message:"请输入最小提现金额"}]}],expression:"['withMinAmt', { rules: [{ required: true, message: '请输入最小提现金额', }] }]"}],attrs:{placeholder:"请输入最小提现金额"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"提现单笔手续费（例:5）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withFeeSingle",{rules:[{required:!0,message:"请输入提现单笔手续费"}]}],expression:"['withFeeSingle', { rules: [{ required: true, message: '请输入提现单笔手续费', }] }]"}],attrs:{placeholder:"请输入提现单笔手续费"}})],1)],1),r("a-col",{attrs:{md:12,lg:6,sm:12}},[r("a-form-item",{attrs:{label:"提现手续费百分比（例:0.005）"}},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withFeePercent",{rules:[{required:!0,message:"请输入提现手续费百分比"}]}],expression:"['withFeePercent', { rules: [{ required: true, message: '请输入提现手续费百分比', }] }]"}],attrs:{placeholder:"请输入提现手续费百分比"}})],1)],1)],1)],1)],1),r("div",{staticClass:"bottomfixed"},[r("div",{staticStyle:{float:"right"}},[r("a-button",{attrs:{type:"primary",loading:e.addUserDialogloading},on:{click:e.OkaddUserdialog}},[e._v(" 保存当前设置 ")])],1)])],1)},s=[],i=(t(27296),t(89077),t(11334)),o=t(34199),l=t.n(o),d={name:"sharessetting",data:function(){return{addUserform:this.$form.createForm(this),loading:!1,fields:["buyFee","sellFee","stayFee","dutyFee","stayMaxDays","buyMinAmt","chargeMinAmt","buyMinNum","forceStopFee","buyMaxAmtPercent","forceStopPercent","hightAndLow","withMinAmt","creaseMaxPercent","kcCreaseMaxPercent","buyMaxNum","cantSellTimes","buySameTimes","buySameNums","buyNumTimes","buyNumLots","stockDays","stockRate","withTimeBegin","withTimeEnd","transAmBegin","transAmEnd","transPmBegin","transPmEnd","transAmBeginUs","transAmEndUs","transPmBeginUs","transPmEndUs","transAmBeginhk","transAmEndhk","transPmBeginhk","transPmEndhk","withFeeSingle","withFeePercent","siteLever","forceStopRemindRatio","vipQcMaxAmt","stockAutoDeal","dzStockAutoDeal","withdrawAmBegin","withdrawAmEnd","withdrawPmBegin","withdrawPmEnd","openWhiteList","rechargeBegin","rechargeEnd"],labelCol:{xs:{span:10},sm:{span:10},md:{span:10}},wrapperCol:{xs:{span:14},sm:{span:14},md:{span:14}},addUserDialogloading:!1,details:{}}},mounted:function(){this.getdetail()},methods:{OkaddUserdialog:function(){var e=this,r=this.$refs.addUserform.form;r.validateFields((function(r,t){r||(e.addUserDialogloading=!0,e.loading=!0,t.id=e.details.id,(0,i.IU)(t).then((function(r){0==r.status?(e.$message.success({content:r.msg,duration:2}),e.getdetail()):e.$message.error({content:r.msg}),e.addUserDialogloading=!1})))}))},getdetail:function(){var e=this,r=this;this.loading=!0,(0,i.WU)().then((function(t){e.details=t.data,e.fields.forEach((function(r){return e.addUserform.getFieldDecorator(r)})),e.addUserform.setFieldsValue(l()(t.data,e.fields)),setTimeout((function(){r.loading=!1}),500)}))}}},u=d,n=t(6367),m=(0,n.A)(u,a,s,!1,null,"71b7d722",null),c=m.exports},34199:function(e,r,t){var a=1/0,s=9007199254740991,i="[object Arguments]",o="[object Function]",l="[object GeneratorFunction]",d="[object Symbol]",u="object"==typeof t.g&&t.g&&t.g.Object===Object&&t.g,n="object"==typeof self&&self&&self.Object===Object&&self,m=u||n||Function("return this")();function c(e,r,t){switch(t.length){case 0:return e.call(r);case 1:return e.call(r,t[0]);case 2:return e.call(r,t[0],t[1]);case 3:return e.call(r,t[0],t[1],t[2])}return e.apply(r,t)}function g(e,r){var t=-1,a=e?e.length:0,s=Array(a);while(++t<a)s[t]=r(e[t],t,e);return s}function p(e,r){var t=-1,a=r.length,s=e.length;while(++t<a)e[s+t]=r[t];return e}var v=Object.prototype,f=v.hasOwnProperty,h=v.toString,w=m.Symbol,b=v.propertyIsEnumerable,x=w?w.isConcatSpreadable:void 0,y=Math.max;function q(e,r,t,a,s){var i=-1,o=e.length;t||(t=P),s||(s=[]);while(++i<o){var l=e[i];r>0&&t(l)?r>1?q(l,r-1,t,a,s):p(s,l):a||(s[s.length]=l)}return s}function S(e,r){return e=Object(e),A(e,r,(function(r,t){return t in e}))}function A(e,r,t){var a=-1,s=r.length,i={};while(++a<s){var o=r[a],l=e[o];t(l,o)&&(i[o]=l)}return i}function N(e,r){return r=y(void 0===r?e.length-1:r,0),function(){var t=arguments,a=-1,s=y(t.length-r,0),i=Array(s);while(++a<s)i[a]=t[r+a];a=-1;var o=Array(r+1);while(++a<r)o[a]=t[a];return o[r]=i,c(e,this,o)}}function P(e){return C(e)||M(e)||!!(x&&e&&e[x])}function F(e){if("string"==typeof e||L(e))return e;var r=e+"";return"0"==r&&1/e==-a?"-0":r}function M(e){return _(e)&&f.call(e,"callee")&&(!b.call(e,"callee")||h.call(e)==i)}var C=Array.isArray;function k(e){return null!=e&&U(e.length)&&!E(e)}function _(e){return D(e)&&k(e)}function E(e){var r=B(e)?h.call(e):"";return r==o||r==l}function U(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=s}function B(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}function D(e){return!!e&&"object"==typeof e}function L(e){return"symbol"==typeof e||D(e)&&h.call(e)==d}var j=N((function(e,r){return null==e?{}:S(e,g(q(r,1),F))}));e.exports=j}}]);