"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[966],{10966:function(t,e,n){n.r(e),n.d(e,{default:function(){return d}});var o=function(){var t=this,e=t._self._c;return e("a-list",{attrs:{itemLayout:"horizontal"}},[e("a-list-item",{scopedSlots:t._u([{key:"actions",fn:function(){return[e("a-switch",{attrs:{checkedChildren:"暗色",unCheckedChildren:"白色",defaultChecked:"dark"===t.navTheme},on:{change:t.onChange}})]},proxy:!0}])},[e("a-list-item-meta",{scopedSlots:t._u([{key:"title",fn:function(){return[e("a",[t._v("风格配色")])]},proxy:!0},{key:"description",fn:function(){return[e("span",[t._v(" 整体风格配色设置 ")])]},proxy:!0}])})],1),e("a-list-item",[e("a-list-item-meta",{scopedSlots:t._u([{key:"title",fn:function(){return[e("a",[t._v("主题色")])]},proxy:!0},{key:"description",fn:function(){return[e("span",[t._v(" 页面风格配色： "),e("a",[t._v(t._s(t.colorFilter(t.primaryColor)))])])]},proxy:!0}])})],1)],1)},r=[],i=(n(42554),n(27296),n(27609),n(21472),n(32429),n(79207),n(80462),n(75379),n(56692),n(89335),[{key:"薄暮",color:"#F5222D"},{key:"火山",color:"#FA541C"},{key:"日暮",color:"#FAAD14"},{key:"明青",color:"#13C2C2"},{key:"极光绿",color:"#52C41A"},{key:"拂晓蓝（默认）",color:"#1890FF"},{key:"极客蓝",color:"#2F54EB"},{key:"酱紫",color:"#722ED1"}]),u=n(56262),a=n(95158),c={dark:"暗色",light:"白色"},l={mixins:[u.t],data:function(){return{}},filters:{themeFilter:function(t){return c[t]}},methods:{colorFilter:function(t){var e=i.find((function(e){return e.color===t}));return e&&e.key},onChange:function(t){t?this.$store.commit(a.RM,a.oF.DARK):this.$store.commit(a.RM,a.oF.LIGHT)}}},p=l,s=n(6367),f=(0,s.A)(p,o,r,!1,null,null,null),d=f.exports},56262:function(t,e,n){n.d(e,{t:function(){return i}});var o=n(82083),r=n(97830),i={computed:(0,o.A)((0,o.A)({},(0,r.aH)({layout:function(t){return t.app.layout},navTheme:function(t){return t.app.theme},primaryColor:function(t){return t.app.color},colorWeak:function(t){return t.app.weak},fixedHeader:function(t){return t.app.fixedHeader},fixedSidebar:function(t){return t.app.fixedSidebar},contentWidth:function(t){return t.app.contentWidth},autoHideHeader:function(t){return t.app.autoHideHeader},isMobile:function(t){return t.app.isMobile},sideCollapsed:function(t){return t.app.sideCollapsed},multiTab:function(t){return t.app.multiTab}})),{},{isTopMenu:function(){return"topmenu"===this.layout}}),methods:{isSideMenu:function(){return!this.isTopMenu}}}}}]);