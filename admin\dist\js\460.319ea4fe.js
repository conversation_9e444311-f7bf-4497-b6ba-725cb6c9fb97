(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[460],{34199:function(e,t,a){var r=1/0,n=9007199254740991,i="[object Arguments]",o="[object Function]",l="[object GeneratorFunction]",s="[object Symbol]",d="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,c="object"==typeof self&&self&&self.Object===Object&&self,u=d||c||Function("return this")();function g(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function m(e,t){var a=-1,r=e?e.length:0,n=Array(r);while(++a<r)n[a]=t(e[a],a,e);return n}function p(e,t){var a=-1,r=t.length,n=e.length;while(++a<r)e[n+a]=t[a];return e}var f=Object.prototype,h=f.hasOwnProperty,v=f.toString,b=u.Symbol,C=f.propertyIsEnumerable,w=b?b.isConcatSpreadable:void 0,y=Math.max;function U(e,t,a,r,n){var i=-1,o=e.length;a||(a=q),n||(n=[]);while(++i<o){var l=e[i];t>0&&a(l)?t>1?U(l,t-1,a,r,n):p(n,l):r||(n[n.length]=l)}return n}function _(e,t){return e=Object(e),N(e,t,(function(t,a){return a in e}))}function N(e,t,a){var r=-1,n=t.length,i={};while(++r<n){var o=t[r],l=e[o];a(l,o)&&(i[o]=l)}return i}function k(e,t){return t=y(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,n=y(a.length-t,0),i=Array(n);while(++r<n)i[r]=a[t+r];r=-1;var o=Array(t+1);while(++r<t)o[r]=a[r];return o[t]=i,g(e,this,o)}}function q(e){return D(e)||P(e)||!!(w&&e&&e[w])}function x(e){if("string"==typeof e||L(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}function P(e){return $(e)&&h.call(e,"callee")&&(!C.call(e,"callee")||v.call(e)==i)}var D=Array.isArray;function F(e){return null!=e&&A(e.length)&&!S(e)}function $(e){return j(e)&&F(e)}function S(e){var t=I(e)?v.call(e):"";return t==o||t==l}function A(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=n}function I(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function j(e){return!!e&&"object"==typeof e}function L(e){return"symbol"==typeof e||j(e)&&v.call(e)==s}var O=k((function(e,t){return null==e?{}:_(e,m(U(t,1),x))}));e.exports=O},70460:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return S}});a(27609);var r=function(){var e=this,t=this,a=t._self._c;return a("page-header-wrapper",[a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"下级代理"}},[a("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getclickagentlist},model:{value:t.agentqueryParam.id,callback:function(e){t.$set(t.agentqueryParam,"id",e)},expression:"agentqueryParam.id"}},t._l(t.clickagentlist,(function(e,r){return a("a-select-option",{key:r,attrs:{value:e.id}},[t._v(" "+t._s("".concat(e.agentName,"/").concat(e.id))+" ")])})),1)],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"代理真实姓名"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写真实姓名"},model:{value:t.agentqueryParam.realName,callback:function(e){t.$set(t.agentqueryParam,"realName",e)},expression:"agentqueryParam.realName"}})],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"用户手机"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写用户手机号"},model:{value:t.agentqueryParam.phone,callback:function(e){t.$set(t.agentqueryParam,"phone",e)},expression:"agentqueryParam.phone"}})],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:function(e){t.agentqueryParam.pageNum=1,t.pagination.current=1,t.getagentlist()}}},[t._v("查询 ")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{icon:"redo"},on:{click:function(){return e.agentqueryParam={}}}},[t._v("重置")])],1)])],1)],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(e){t.$refs.addagentdialog.addUserdialog=!0}}},[t._v("添加下级代理")])],1)]),a("a-card",{attrs:{bordered:!1}},[a("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.agentlist,rowKey:"id"},scopedSlots:t._u([{key:"agentName",fn:function(e,r){return a("span",{},[[a("div",[a("span",[t._v(t._s(r.agentName)+"（"+t._s(r.id)+"）")])])]],2)}},{key:"parentName",fn:function(e,r){return a("span",{},[[a("div",[a("span",[t._v(t._s(r.parentName))]),r.parentId?a("span",[t._v("("+t._s(r.parentId)+")")]):t._e()])]],2)}},{key:"action",fn:function(e,r){return[a("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.$refs.editCapitalAgentdialog.getEditorder(r)}},slot:"action"},[t._v("修改资金")]),a("a-divider",{attrs:{type:"vertical"}}),a("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getDelagent(r)}},slot:"action"},[t._v("删除代理")]),a("a-divider",{attrs:{type:"vertical"}}),a("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.$refs.EditAgentinfodialog.getEditorder(r)}},slot:"action"},[t._v("修改代理")])]}}])})],1),a("editCapitalAgentdialog",{ref:"editCapitalAgentdialog",attrs:{getinit:t.geteditinit}}),a("EditAgentinfodialog",{ref:"EditAgentinfodialog",attrs:{getinit:t.geteditinit}}),a("addagentdialog",{ref:"addagentdialog",attrs:{getinit:t.getinit}})],1)},n=[],i=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"账户扣入款",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"用户id",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentId",{}],expression:"['agentId', {}]"}],attrs:{placeholder:"请输入用户id",disabled:""}})],1),t("a-form-item",{attrs:{label:"金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["amt",{rules:[{required:!0,message:"请输入金额"}]}],expression:"['amt', { rules: [{ required: true, message: '请输入金额', }] }]"}],attrs:{placeholder:"请输入金额"}})],1),t("a-form-item",{attrs:{label:"扣入款",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["direction",{rules:[{required:!0,message:"请选择扣入款"}]}],expression:"['direction', { rules: [{ required: true, message: '请选择扣入款', }] }]"}],attrs:{placeholder:"请选择扣入款"}},[t("a-select-option",{attrs:{value:"1"}},[e._v("扣款")]),t("a-select-option",{attrs:{value:"0"}},[e._v("入款")])],1)],1)],1)],1)],1)},o=[],l=(a(27296),a(89077),a(18697)),s=a(34199),d=a.n(s),c={components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["amt","direction","agentId"],currentDetails:{}}},methods:{getEditorder:function(e){var t=this;this.currentDetails=e,this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(d()(e,this.fields)),this.editUserform.setFieldsValue(d()({agentId:e.id},this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(e.editUserDialogloading=!0,(0,l.gs)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},u=c,g=a(6367),m=(0,g.A)(u,i,o,!1,null,null,null),p=m.exports,f=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"修改代理信息",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"代理ID",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["id",{}],expression:"['id', {}]"}],attrs:{placeholder:"请输入代理ID",disabled:""}})],1),t("a-form-item",{attrs:{label:"代理手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentPhone",{rules:[{required:!0,message:"请输入代理手机号"}]}],expression:"['agentPhone', {rules: [{ required: true, message: '请输入代理手机号', }] }]"}],attrs:{placeholder:"请输入代理手机号"}})],1),t("a-form-item",{attrs:{label:"真实姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentRealName",{rules:[{required:!0,message:"请输入真实姓名"}]}],expression:"['agentRealName', {rules: [{ required: true, message: '请输入真实姓名', }] }]"}],attrs:{placeholder:"请输入真实姓名"}})],1),t("a-form-item",{attrs:{label:"密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentPwd",{}],expression:"['agentPwd', {}]"}],attrs:{placeholder:"请输入密码"}})],1),t("a-form-item",{attrs:{label:"杠杆倍数",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["siteLever",{}],expression:"['siteLever', {}]"}],attrs:{placeholder:"请输入杠杆倍数"}}),t("span",[e._v("（例:100/50）")])],1),t("a-form-item",{attrs:{label:"代理状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["isLock",{rules:[{required:!0,message:"请选择代理状态"}]}],expression:"['isLock', {rules: [{ required: true, message: '请选择代理状态', }]}]"}],attrs:{placeholder:"请选择代理状态"}},[t("a-select-option",{attrs:{value:0}},[e._v("正常")]),t("a-select-option",{attrs:{value:1}},[e._v("锁定")])],1)],1)],1)],1)],1)},h=[],v={components:{},props:{getinit:{type:Function,default:function(){}},agentlist:{type:Array}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["id","agentPhone","agentRealName","agentPwd","siteLever","isLock"],currentDetails:{}}},methods:{getEditorder:function(e){var t=this;this.currentDetails=e,this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(d()(e,this.fields)),this.editUserform.setFieldsValue(d()({agentPwd:""},this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(e.editUserDialogloading=!0,(0,l.hj)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},b=v,C=(0,g.A)(b,f,h,!1,null,null,null),w=C.exports,y=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"添加下级代理",width:500,visible:e.addUserdialog,confirmLoading:e.addUserDialogloading},on:{ok:e.OkaddUserdialog,cancel:e.CanceladdUserdialog}},[t("a-form",{ref:"addUserform",attrs:{form:e.addUserform}},[t("a-form-item",{attrs:{label:"上级代理",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["parentId",{rules:[{required:!0,message:"请选择上级代理"}]}],expression:"['parentId', { rules: [{ required: true, message: '请选择上级代理', }] }]"}],attrs:{placeholder:"请选择上级代理"}},[t("a-select-option",{attrs:{value:"0"}},[e._v("总代理")]),e._l(e.agentList,(function(a){return[t("a-select-option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.agentName))]),e._l(a.children,(function(a){return[t("a-select-option",{key:a.id,attrs:{value:a.id}},[e._v("| - "+e._s(a.agentName))]),e._l(a.children,(function(a){return[t("a-select-option",{key:a.id,attrs:{value:a.id}},[e._v("| --- "+e._s(a.agentName))]),e._l(a.children,(function(a){return[t("a-select-option",{key:a.id,attrs:{value:a.id}},[e._v("| ------ "+e._s(a.agentName))]),e._l(a.children,(function(a){return[t("a-select-option",{key:a.id,attrs:{value:a.id}},[e._v("| --------- "+e._s(a.agentName))])]}))]}))]}))]}))]}))],2)],1),t("a-form-item",{attrs:{label:"代理名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentName",{rules:[{required:!0,message:"请输入代理名称"}]}],expression:"['agentName', { rules: [{ required: true, message: '请输入代理名称', }] }]"}],attrs:{placeholder:"请输入代理名称"}})],1),t("a-form-item",{attrs:{label:"代理手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentPhone",{rules:[{required:!0,message:"请输入代理手机号"}]}],expression:"['agentPhone', { rules: [{ required: true, message: '请输入代理手机号', }] }]"}],attrs:{placeholder:"请输入代理手机号"}})],1),t("a-form-item",{attrs:{label:"密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentPwd",{rules:[{required:!0,message:"请输入密码"}]}],expression:"['agentPwd', { rules: [{ required: true, message: '请输入密码', }] }]"}],attrs:{placeholder:"请输入密码"}})],1),t("a-form-item",{attrs:{label:"真实姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentRealName",{rules:[{required:!0,message:"请输入真实姓名"}]}],expression:"['agentRealName', { rules: [{ required: true, message: '请输入真实姓名', }] }]"}],attrs:{placeholder:"请输入真实姓名"}})],1)],1)],1)],1)},U=[],_=(a(65464),{components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},addUserform:this.$form.createForm(this),addUserdialog:!1,addUserDialogloading:!1,agentList:[]}},watch:{addUserdialog:function(e){var t=this;e&&(0,l.cG)().then((function(e){t.agentList=e.data}))}},methods:{generateLabel:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=" ".repeat(a),n=r+e.agentName;return e.children&&e.children.length>0&&e.children.forEach((function(e){n+=t.generateLabel(e,a+1)})),n},CanceladdUserdialog:function(){this.addUserdialog=!1;var e=this.$refs.addUserform.form;e.resetFields()},OkaddUserdialog:function(){var e=this,t=this.$refs.addUserform.form;t.validateFields((function(a,r){a||(e.addUserDialogloading=!0,(0,l.ts)(r).then((function(a){0==a.status?(e.addUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.addUserDialogloading=!1})))}))}}}),N=_,k=(0,g.A)(N,y,U,!1,null,null,null),q=k.exports,x=a(46279),P=a.n(x),D={name:"Agentlist",components:{editCapitalAgentdialog:p,EditAgentinfodialog:w,addagentdialog:q},data:function(){var e=this;return{columns:[{title:"所属代理（Id）",scopedSlots:{customRender:"agentName"},align:"center"},{title:"代理编码",dataIndex:"agentCode",align:"center"},{title:"上级代理",dataIndex:"parentName",align:"center",scopedSlots:{customRender:"parentName"}},{title:"代理手机号",dataIndex:"agentPhone",align:"center"},{title:"真实姓名",dataIndex:"agentRealName",align:"center"},{title:"总资金",dataIndex:"totalMoney",align:"center",customRender:function(e,t,a){return e}},{title:"代理状态",dataIndex:"isLock",align:"center",scopedSlots:{customRender:"isLock"}},{title:"注册时间",dataIndex:"addTime",align:"center",customRender:function(e,t,a){return P()(e).format("YYYY-MM-DD HH:mm:ss")}},{title:"操作",key:"action",align:"center",scopedSlots:{customRender:"action"}}],pagination:{total:0,current:1,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,agentqueryParam:{id:void 0,phone:"",realName:"",pageNum:1,pageSize:10},agentloading:!1,agentlist:[],clickagentlist:[]}},created:function(){this.getagentlist()},methods:{getDelagent:function(e){var t=this;this.$confirm({title:"提示",content:"确认删除代理？此操作不可恢复",onOk:function(){var a={agentId:e.id};(0,l.oT)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getinit()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},getinit:function(){this.queryParam={id:void 0,phone:"",realName:"",pageNum:1,pageSize:10},this.pagination.current=1,this.getagentlist()},geteditinit:function(){this.getagentlist()},getagentlist:function(){var e=this,t=this;this.loading=!0,(0,l.vP)(this.agentqueryParam).then((function(a){e.agentlist=a.data.list,e.pagination.total=a.data.total,setTimeout((function(){t.loading=!1}),500)}))},getclickagentlist:function(){var e=this,t=this;this.agentloading=!0;var a={pageNum:1,pageSize:100};(0,l.vP)(a).then((function(a){e.clickagentlist=a.data.list,setTimeout((function(){t.agentloading=!1}),500)}))},onPageChange:function(e,t){this.agentqueryParam.pageNum=e,this.pagination.current=e,this.getagentlist()},onSizeChange:function(e,t){this.agentqueryParam.pageNum=e,this.pagination.current=e,this.agentqueryParam.pageSize=t,this.getagentlist()}}},F=D,$=(0,g.A)(F,r,n,!1,null,null,null),S=$.exports}}]);