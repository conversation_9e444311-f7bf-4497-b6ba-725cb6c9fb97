"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[214],{214:function(e,a,t){t.r(a),t.d(a,{default:function(){return g}});var r=function(){var e=this,a=e._self._c;return a("div",{staticClass:"user-fund-flow"},[a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"search-form-wrapper"},[a("a-form",{staticClass:"ant-advanced-search-form",attrs:{form:e.form},on:{submit:e.handleSearch}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId"],expression:"['userId']"}],attrs:{placeholder:"请输入用户ID",allowClear:""}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"用户姓名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["realName"],expression:"['realName']"}],attrs:{placeholder:"请输入用户姓名",allowClear:""}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"手机号"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone"],expression:"['phone']"}],attrs:{placeholder:"请输入手机号",allowClear:""}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"代理ID"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentId"],expression:"['agentId']"}],attrs:{placeholder:"请输入代理ID",allowClear:""}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"代理名称"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentName"],expression:"['agentName']"}],attrs:{placeholder:"请输入代理名称",allowClear:""}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"账变类型"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["changeType"],expression:"['changeType']"}],attrs:{placeholder:"请选择账变类型",allowClear:""}},e._l(e.changeTypes,(function(t,r){return a("a-select-option",{key:r,attrs:{value:r}},[e._v(" "+e._s(t)+" ")])})),1)],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"关联订单号"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["relatedOrderSn"],expression:"['relatedOrderSn']"}],attrs:{placeholder:"请输入关联订单号",allowClear:""}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"变动金额"}},[a("a-input-group",{attrs:{compact:""}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["minChangeAmount"],expression:"['minChangeAmount']"}],staticStyle:{width:"40%"},attrs:{placeholder:"最小金额"}}),a("a-input",{staticStyle:{width:"20%","text-align":"center","pointer-events":"none"},attrs:{placeholder:"~",disabled:""}}),a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["maxChangeAmount"],expression:"['maxChangeAmount']"}],staticStyle:{width:"40%"},attrs:{placeholder:"最大金额"}})],1)],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"业务描述"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["businessDesc"],expression:"['businessDesc']"}],attrs:{placeholder:"请输入业务描述",allowClear:""}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"操作人姓名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["operatorName"],expression:"['operatorName']"}],attrs:{placeholder:"请输入操作人姓名",allowClear:""}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-item",{attrs:{label:"时间范围"}},[a("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["timeRange"],expression:"['timeRange']"}],staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:["开始时间","结束时间"]}})],1)],1)],1),a("a-row",[a("a-col",{staticStyle:{"text-align":"right"},attrs:{span:24}},[a("a-button",{attrs:{type:"primary","html-type":"submit"}},[a("a-icon",{attrs:{type:"search"}}),e._v(" 查询 ")],1),a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.handleReset}},[a("a-icon",{attrs:{type:"reload"}}),e._v(" 重置 ")],1)],1)],1)],1)],1),a("div",{staticClass:"table-wrapper"},[a("a-table",{attrs:{columns:e.columns,"data-source":e.dataSource,pagination:e.pagination,loading:e.loading,scroll:{x:1800},"row-key":"id",size:"small"},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"changeAmount",fn:function(t,r){return[a("span",{style:{color:r.changeAmount>=0?"#52c41a":"#f5222d"}},[e._v(" "+e._s(r.changeAmount>=0?"+":"")+e._s(t)+" ")])]}},{key:"changeType",fn:function(t,r){return[a("a-tag",{attrs:{color:e.getChangeTypeColor(t)}},[e._v(" "+e._s(r.changeTypeDesc)+" ")])]}},{key:"createTime",fn:function(a){return[e._v(" "+e._s(e.formatTime(a))+" ")]}},{key:"action",fn:function(t,r){return[a("a-button",{attrs:{type:"link",size:"small",disabled:!r.relatedOrderSn},on:{click:function(a){return e.showFlowRecords(r)}}},[e._v(" 查询流水记录 ")])]}}])})],1)]),a("a-modal",{attrs:{title:"流水记录详情",visible:e.modalVisible,width:1200,footer:null,destroyOnClose:!0},on:{cancel:e.closeModal}},[a("div",{staticClass:"flow-records-modal"},[a("div",{staticStyle:{"margin-bottom":"16px"}},[a("strong",[e._v("关联订单号：")]),e._v(e._s(e.currentOrderSn)+" ")]),a("a-table",{attrs:{columns:e.modalColumns,"data-source":e.modalDataSource,loading:e.modalLoading,pagination:!1,"row-key":"id",size:"small"},scopedSlots:e._u([{key:"changeAmount",fn:function(t,r){return[a("span",{style:{color:r.changeAmount>=0?"#52c41a":"#f5222d"}},[e._v(" "+e._s(r.changeAmount>=0?"+":"")+e._s(t)+" ")])]}},{key:"changeType",fn:function(t,r){return[a("a-tag",{attrs:{color:e.getChangeTypeColor(t)}},[e._v(" "+e._s(r.changeTypeDesc)+" ")])]}},{key:"createTime",fn:function(a){return[e._v(" "+e._s(e.formatTime(a))+" ")]}}])})],1)])],1)},n=[],o=t(53177),s=t(82083),i=t(93251),l=(t(27609),t(71008)),c=t(46279),d=t.n(c),m={name:"UserFundFlow",data:function(){return{form:this.$form.createForm(this),loading:!1,dataSource:[],changeTypes:[],pagination:{current:1,pageSize:20,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e,a){return"共 ".concat(e," 条记录，第 ").concat(a[0],"-").concat(a[1]," 条")}},columns:[{title:"用户ID",dataIndex:"userId",width:80,fixed:"left"},{title:"用户姓名",dataIndex:"realName",width:100,fixed:"left"},{title:"手机号",dataIndex:"phone",width:120},{title:"代理名称",dataIndex:"agentName",width:120},{title:"账变类型",dataIndex:"changeType",width:100,scopedSlots:{customRender:"changeType"}},{title:"变动金额",dataIndex:"changeAmount",width:120,align:"right",scopedSlots:{customRender:"changeAmount"}},{title:"变动前可用",dataIndex:"beforeEnableAmount",width:120,align:"right"},{title:"变动后可用",dataIndex:"afterEnableAmount",width:120,align:"right"},{title:"关联订单号",dataIndex:"relatedOrderSn",width:150},{title:"业务描述",dataIndex:"businessDesc",width:200},{title:"手续费",dataIndex:"feeAmount",width:100,align:"right"},{title:"操作人",dataIndex:"operatorName",width:100},{title:"创建时间",dataIndex:"createTime",width:160,scopedSlots:{customRender:"createTime"}},{title:"操作",width:120,fixed:"right",scopedSlots:{customRender:"action"}}],modalVisible:!1,modalLoading:!1,modalDataSource:[],currentOrderSn:"",modalColumns:[{title:"用户ID",dataIndex:"userId",width:80},{title:"用户姓名",dataIndex:"realName",width:100},{title:"账变类型",dataIndex:"changeType",width:100,scopedSlots:{customRender:"changeType"}},{title:"变动金额",dataIndex:"changeAmount",width:120,align:"right",scopedSlots:{customRender:"changeAmount"}},{title:"业务描述",dataIndex:"businessDesc",width:200},{title:"创建时间",dataIndex:"createTime",width:160,scopedSlots:{customRender:"createTime"}}]}},mounted:function(){this.fetchData(),this.getChangeTypeOptions()},methods:{fetchData:function(){var e=arguments,a=this;return(0,i.A)((0,o.A)().m((function t(){var r,n,i,c;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:return r=e.length>0&&void 0!==e[0]?e[0]:{},a.loading=!0,t.p=1,n=(0,s.A)({pageNum:a.pagination.current,pageSize:a.pagination.pageSize,orderBy:"create_time",orderDirection:"DESC"},r),t.n=2,(0,l.Fj)(n);case 2:i=t.v,0===i.status?(a.dataSource=i.data.list||[],a.pagination=(0,s.A)((0,s.A)({},a.pagination),{},{current:i.data.pageNum,pageSize:i.data.pageSize,total:i.data.total})):a.$message.error(i.msg||"查询失败"),t.n=4;break;case 3:t.p=3,c=t.v,console.error("获取用户资金流水失败:",c),a.$message.error("查询失败，请稍后重试");case 4:return t.p=4,a.loading=!1,t.f(4);case 5:return t.a(2)}}),t,null,[[1,3,4,5]])})))()},handleSearch:function(e){var a=this;e.preventDefault(),this.form.validateFields((function(e,t){if(!e){var r=(0,s.A)({},t);t.timeRange&&2===t.timeRange.length&&(r.startTime=t.timeRange[0].format("YYYY-MM-DD HH:mm:ss"),r.endTime=t.timeRange[1].format("YYYY-MM-DD HH:mm:ss"),delete r.timeRange),a.pagination.current=1,a.fetchData(r)}}))},handleReset:function(){this.form.resetFields(),this.pagination.current=1,this.fetchData()},handleTableChange:function(e,a,t){this.pagination=(0,s.A)((0,s.A)({},this.pagination),{},{current:e.current,pageSize:e.pageSize});var r=this.form.getFieldsValue(),n=(0,s.A)({},r);r.timeRange&&2===r.timeRange.length&&(n.startTime=r.timeRange[0].format("YYYY-MM-DD HH:mm:ss"),n.endTime=r.timeRange[1].format("YYYY-MM-DD HH:mm:ss"),delete n.timeRange),this.fetchData(n)},getChangeTypeColor:function(e){var a={RECHARGE:"green",WITHDRAW_APPLY:"red",WITHDRAW_SUCCESS:"volcano",WITHDRAW_REJECT:"red",WITHDRAW_CANCEL:"orange",STOCK_BUY:"blue",STOCK_SELL:"geekblue",STOCK_PARTIAL_REFUND:"cyan",STOCK_ENTRUST_CANCEL:"blue",STOCK_AUDIT_REJECT:"blue",STOCK_SUBSCRIBE:"purple",STOCK_SUBSCRIBE_REFUND:"magenta",TRANSFER_IN:"cyan",TRANSFER_OUT:"geekblue",ADMIN_ADD_MONEY:"gold",ADMIN_REDUCE_MONEY:"orange",FUNDS_APPLY:"magenta",FUNDS_AUDIT_REJECT:"pink",STAY_FEE_DEDUCT:"yellow",SYSTEM_ADJUST:"lime",OTHER:"volcano"};return a[e]||"processing"},formatTime:function(e){return e?d()(e).format("YYYY-MM-DD HH:mm:ss"):"-"},showFlowRecords:function(e){var a=this;return(0,i.A)((0,o.A)().m((function t(){var r,n;return(0,o.A)().w((function(t){while(1)switch(t.n){case 0:if(e.relatedOrderSn){t.n=1;break}return a.$message.warning("该记录没有关联订单号"),t.a(2);case 1:return a.currentOrderSn=e.relatedOrderSn,a.modalVisible=!0,a.modalLoading=!0,t.p=2,t.n=3,(0,l.oW)({relatedOrderSn:e.relatedOrderSn});case 3:r=t.v,0===r.status?a.modalDataSource=r.data||[]:a.$message.error(r.msg||"查询失败"),t.n=5;break;case 4:t.p=4,n=t.v,console.error("查询流水记录失败:",n),a.$message.error("查询失败，请稍后重试");case 5:return t.p=5,a.modalLoading=!1,t.f(5);case 6:return t.a(2)}}),t,null,[[2,4,5,6]])})))()},closeModal:function(){this.modalVisible=!1,this.modalDataSource=[],this.currentOrderSn=""},getChangeTypeOptions:function(){var e=this;return(0,i.A)((0,o.A)().m((function a(){var t,r;return(0,o.A)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,(0,l._E)();case 1:t=a.v,0===t.status?e.changeTypes=t.data||{}:(console.error("获取账变类型失败:",t.msg),e.changeTypes={RECHARGE:"充值",WITHDRAW_APPLY:"提现申请",WITHDRAW_SUCCESS:"提现成功",WITHDRAW_REJECT:"提现驳回",STOCK_BUY:"买入股票",STOCK_SELL:"卖出股票",ADMIN_ADD_MONEY:"管理员上分",ADMIN_REDUCE_MONEY:"管理员下分"}),a.n=3;break;case 2:a.p=2,r=a.v,console.error("获取账变类型异常:",r),e.changeTypes={RECHARGE:"充值",WITHDRAW_APPLY:"提现申请",WITHDRAW_SUCCESS:"提现成功",WITHDRAW_REJECT:"提现驳回",STOCK_BUY:"买入股票",STOCK_SELL:"卖出股票",ADMIN_ADD_MONEY:"管理员上分",ADMIN_REDUCE_MONEY:"管理员下分"};case 3:return a.a(2)}}),a,null,[[0,2]])})))()}}},u=m,p=t(6367),h=(0,p.A)(u,r,n,!1,null,"2a248836",null),g=h.exports}}]);