<template>
    <div class="user-fund-flow">
        <a-card :bordered="false">
            <!-- 搜索表单 -->
            <div class="search-form-wrapper">
                <a-form class="ant-advanced-search-form" :form="form" @submit="handleSearch">
                    <a-row :gutter="24">
                        <a-col :span="6">
                            <a-form-item label="用户ID">
                                <a-input v-decorator="['userId']" placeholder="请输入用户ID" allowClear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item label="用户姓名">
                                <a-input v-decorator="['realName']" placeholder="请输入用户姓名" allowClear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item label="手机号">
                                <a-input v-decorator="['phone']" placeholder="请输入手机号" allowClear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item label="代理ID">
                                <a-input v-decorator="['agentId']" placeholder="请输入代理ID" allowClear />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="24">
                        <a-col :span="6">
                            <a-form-item label="代理名称">
                                <a-input v-decorator="['agentName']" placeholder="请输入代理名称" allowClear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item label="账变类型">
                                <a-select v-decorator="['changeType']" placeholder="请选择账变类型" allowClear>
                                    <a-select-option v-for="(desc, code) in changeTypes" :key="code" :value="code">
                                        {{ desc }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item label="关联订单号">
                                <a-input v-decorator="['relatedOrderSn']" placeholder="请输入关联订单号" allowClear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item label="变动金额">
                                <a-input-group compact>
                                    <a-input v-decorator="['minChangeAmount']" style="width: 40%" placeholder="最小金额" />
                                    <a-input style="width: 20%; text-align: center; pointer-events: none;"
                                        placeholder="~" disabled />
                                    <a-input v-decorator="['maxChangeAmount']" style="width: 40%" placeholder="最大金额" />
                                </a-input-group>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="24">
                        <a-col :span="6">
                            <a-form-item label="业务描述">
                                <a-input v-decorator="['businessDesc']" placeholder="请输入业务描述" allowClear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item label="操作人姓名">
                                <a-input v-decorator="['operatorName']" placeholder="请输入操作人姓名" allowClear />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="时间范围">
                                <a-range-picker v-decorator="['timeRange']" style="width: 100%" show-time
                                    format="YYYY-MM-DD HH:mm:ss" :placeholder="['开始时间', '结束时间']" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row>
                        <a-col :span="24" style="text-align: right">
                            <a-button type="primary" html-type="submit">
                                <a-icon type="search" />
                                查询
                            </a-button>
                            <a-button style="margin-left: 8px" @click="handleReset">
                                <a-icon type="reload" />
                                重置
                            </a-button>
                        </a-col>
                    </a-row>
                </a-form>
            </div>

            <!-- 数据表格 -->
            <div class="table-wrapper">
                <a-table :columns="columns" :data-source="dataSource" :pagination="pagination" :loading="loading"
                    :scroll="{ x: 1800 }" row-key="id" @change="handleTableChange" size="small">
                    <template slot="changeAmount" slot-scope="text, record">
                        <span :style="{ color: record.changeAmount >= 0 ? '#52c41a' : '#f5222d' }">
                            {{ record.changeAmount >= 0 ? '+' : '' }}{{ text }}
                        </span>
                    </template>

                    <template slot="changeType" slot-scope="text, record">
                        <a-tag :color="getChangeTypeColor(text)">
                            {{ record.changeTypeDesc }}
                        </a-tag>
                    </template>

                    <template slot="createTime" slot-scope="text">
                        {{ formatTime(text) }}
                    </template>

                    <template slot="action" slot-scope="text, record">
                        <a-button type="link" size="small" @click="showFlowRecords(record)"
                            :disabled="!record.relatedOrderSn">
                            查询流水记录
                        </a-button>
                    </template>
                </a-table>
            </div>
        </a-card>

        <!-- 流水记录弹出框 -->
        <a-modal title="流水记录详情" :visible="modalVisible" :width="1200" @cancel="closeModal" :footer="null"
            :destroyOnClose="true">
            <div class="flow-records-modal">
                <div style="margin-bottom: 16px;">
                    <strong>关联订单号：</strong>{{ currentOrderSn }}
                </div>

                <a-table :columns="modalColumns" :data-source="modalDataSource" :loading="modalLoading"
                    :pagination="false" row-key="id" size="small">
                    <template slot="changeAmount" slot-scope="text, record">
                        <span :style="{ color: record.changeAmount >= 0 ? '#52c41a' : '#f5222d' }">
                            {{ record.changeAmount >= 0 ? '+' : '' }}{{ text }}
                        </span>
                    </template>

                    <template slot="changeType" slot-scope="text, record">
                        <a-tag :color="getChangeTypeColor(text)">
                            {{ record.changeTypeDesc }}
                        </a-tag>
                    </template>

                    <template slot="createTime" slot-scope="text">
                        {{ formatTime(text) }}
                    </template>
                </a-table>
            </div>
        </a-modal>
    </div>
</template>

<script>
import { getUserFundFlowList, queryFundFlowByOrderSn, getChangeTypes } from '@/api/capital'
import moment from 'moment'

export default {
    name: 'UserFundFlow',
    data() {
        return {
            form: this.$form.createForm(this),
            loading: false,
            dataSource: [],
            changeTypes: [], // 账变类型选项
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`
            },
            columns: [
                {
                    title: '用户ID',
                    dataIndex: 'userId',
                    width: 80,
                    fixed: 'left'
                },
                {
                    title: '用户姓名',
                    dataIndex: 'realName',
                    width: 100,
                    fixed: 'left'
                },
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    width: 120
                },
                {
                    title: '代理名称',
                    dataIndex: 'agentName',
                    width: 120
                },
                {
                    title: '账变类型',
                    dataIndex: 'changeType',
                    width: 100,
                    scopedSlots: { customRender: 'changeType' }
                },
                {
                    title: '变动金额',
                    dataIndex: 'changeAmount',
                    width: 120,
                    align: 'right',
                    scopedSlots: { customRender: 'changeAmount' }
                },
                // {
                //     title: '变动前总额',
                //     dataIndex: 'beforeTotalAmount',
                //     width: 120,
                //     align: 'right'
                // },
                // {
                //     title: '变动后总额',
                //     dataIndex: 'afterTotalAmount',
                //     width: 120,
                //     align: 'right'
                // },
                {
                    title: '变动前可用',
                    dataIndex: 'beforeEnableAmount',
                    width: 120,
                    align: 'right'
                },
                {
                    title: '变动后可用',
                    dataIndex: 'afterEnableAmount',
                    width: 120,
                    align: 'right'
                },
                {
                    title: '关联订单号',
                    dataIndex: 'relatedOrderSn',
                    width: 150
                },
                {
                    title: '业务描述',
                    dataIndex: 'businessDesc',
                    width: 200
                },
                {
                    title: '手续费',
                    dataIndex: 'feeAmount',
                    width: 100,
                    align: 'right'
                },
                {
                    title: '操作人',
                    dataIndex: 'operatorName',
                    width: 100
                },
                {
                    title: '创建时间',
                    dataIndex: 'createTime',
                    width: 160,
                    scopedSlots: { customRender: 'createTime' }
                },
                {
                    title: '操作',
                    width: 120,
                    fixed: 'right',
                    scopedSlots: { customRender: 'action' }
                }
            ],
            // Modal相关
            modalVisible: false,
            modalLoading: false,
            modalDataSource: [],
            currentOrderSn: '',
            modalColumns: [
                {
                    title: '用户ID',
                    dataIndex: 'userId',
                    width: 80
                },
                {
                    title: '用户姓名',
                    dataIndex: 'realName',
                    width: 100
                },
                {
                    title: '账变类型',
                    dataIndex: 'changeType',
                    width: 100,
                    scopedSlots: { customRender: 'changeType' }
                },
                {
                    title: '变动金额',
                    dataIndex: 'changeAmount',
                    width: 120,
                    align: 'right',
                    scopedSlots: { customRender: 'changeAmount' }
                },
                {
                    title: '业务描述',
                    dataIndex: 'businessDesc',
                    width: 200
                },
                {
                    title: '创建时间',
                    dataIndex: 'createTime',
                    width: 160,
                    scopedSlots: { customRender: 'createTime' }
                }
            ]
        }
    },
    mounted() {
        this.fetchData()
        this.getChangeTypeOptions()
    },
    methods: {
        // 获取数据
        async fetchData(params = {}) {
            this.loading = true
            try {
                const searchParams = {
                    pageNum: this.pagination.current,
                    pageSize: this.pagination.pageSize,
                    orderBy: 'create_time',
                    orderDirection: 'DESC',
                    ...params
                }

                const response = await getUserFundFlowList(searchParams)

                if (response.status === 0) {
                    this.dataSource = response.data.list || []
                    this.pagination = {
                        ...this.pagination,
                        current: response.data.pageNum,
                        pageSize: response.data.pageSize,
                        total: response.data.total
                    }
                } else {
                    this.$message.error(response.msg || '查询失败')
                }
            } catch (error) {
                console.error('获取用户资金流水失败:', error)
                this.$message.error('查询失败，请稍后重试')
            } finally {
                this.loading = false
            }
        },

        // 搜索
        handleSearch(e) {
            e.preventDefault()
            this.form.validateFields((err, values) => {
                if (!err) {
                    const searchParams = { ...values }

                    // 处理时间范围
                    if (values.timeRange && values.timeRange.length === 2) {
                        searchParams.startTime = values.timeRange[0].format('YYYY-MM-DD HH:mm:ss')
                        searchParams.endTime = values.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
                        delete searchParams.timeRange
                    }

                    // 重置分页
                    this.pagination.current = 1
                    this.fetchData(searchParams)
                }
            })
        },

        // 重置搜索
        handleReset() {
            this.form.resetFields()
            this.pagination.current = 1
            this.fetchData()
        },

        // 表格变化处理
        handleTableChange(pagination, filters, sorter) {
            this.pagination = {
                ...this.pagination,
                current: pagination.current,
                pageSize: pagination.pageSize
            }

            // 获取当前搜索参数
            const formValues = this.form.getFieldsValue()
            const searchParams = { ...formValues }

            // 处理时间范围
            if (formValues.timeRange && formValues.timeRange.length === 2) {
                searchParams.startTime = formValues.timeRange[0].format('YYYY-MM-DD HH:mm:ss')
                searchParams.endTime = formValues.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
                delete searchParams.timeRange
            }

            this.fetchData(searchParams)
        },

        // 获取账变类型颜色
        getChangeTypeColor(type) {
            const colorMap = {
                // 充值相关 - 绿色系
                'RECHARGE': 'green',

                // 提现相关 - 红色系
                'WITHDRAW_APPLY': 'red',
                'WITHDRAW_SUCCESS': 'volcano',
                'WITHDRAW_REJECT': 'red',
                'WITHDRAW_CANCEL': 'orange',

                // 股票交易 - 蓝色系
                'STOCK_BUY': 'blue',
                'STOCK_SELL': 'geekblue',
                'STOCK_PARTIAL_REFUND': 'cyan',
                'STOCK_ENTRUST_CANCEL': 'blue',
                'STOCK_AUDIT_REJECT': 'blue',

                // 新股申购 - 紫色系
                'STOCK_SUBSCRIBE': 'purple',
                'STOCK_SUBSCRIBE_REFUND': 'magenta',

                // 账户转账 - 青色系
                'TRANSFER_IN': 'cyan',
                'TRANSFER_OUT': 'geekblue',

                // 管理员操作 - 橙色系
                'ADMIN_ADD_MONEY': 'gold',
                'ADMIN_REDUCE_MONEY': 'orange',

                // 配资相关 - 品红色系
                'FUNDS_APPLY': 'magenta',
                'FUNDS_AUDIT_REJECT': 'pink',

                // 费用扣除 - 黄色系
                'STAY_FEE_DEDUCT': 'yellow',

                // 系统操作 - 灰色系
                'SYSTEM_ADJUST': 'lime',

                // 其他 - 默认色
                'OTHER': 'volcano'
            }
            return colorMap[type] || 'processing'
        },

        // 格式化时间
        formatTime(time) {
            return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'
        },

        // 显示流水记录
        async showFlowRecords(record) {
            if (!record.relatedOrderSn) {
                this.$message.warning('该记录没有关联订单号')
                return
            }

            this.currentOrderSn = record.relatedOrderSn
            this.modalVisible = true
            this.modalLoading = true

            try {
                const response = await queryFundFlowByOrderSn({
                    relatedOrderSn: record.relatedOrderSn
                })

                if (response.status === 0) {
                    this.modalDataSource = response.data || []
                } else {
                    this.$message.error(response.msg || '查询失败')
                }
            } catch (error) {
                console.error('查询流水记录失败:', error)
                this.$message.error('查询失败，请稍后重试')
            } finally {
                this.modalLoading = false
            }
        },

        // 关闭弹出框
        closeModal() {
            this.modalVisible = false
            this.modalDataSource = []
            this.currentOrderSn = ''
        },

        // 获取账变类型选项
        async getChangeTypeOptions() {
            try {
                const response = await getChangeTypes()
                if (response.status === 0) {
                    this.changeTypes = response.data || {}
                } else {
                    console.error('获取账变类型失败:', response.msg)
                    // 如果接口失败，使用默认选项
                    this.changeTypes = {
                        'RECHARGE': '充值',
                        'WITHDRAW_APPLY': '提现申请',
                        'WITHDRAW_SUCCESS': '提现成功',
                        'WITHDRAW_REJECT': '提现驳回',
                        'STOCK_BUY': '买入股票',
                        'STOCK_SELL': '卖出股票',
                        'ADMIN_ADD_MONEY': '管理员上分',
                        'ADMIN_REDUCE_MONEY': '管理员下分'
                    }
                }
            } catch (error) {
                console.error('获取账变类型异常:', error)
                // 如果接口异常，使用默认选项
                this.changeTypes = {
                    'RECHARGE': '充值',
                    'WITHDRAW_APPLY': '提现申请',
                    'WITHDRAW_SUCCESS': '提现成功',
                    'WITHDRAW_REJECT': '提现驳回',
                    'STOCK_BUY': '买入股票',
                    'STOCK_SELL': '卖出股票',
                    'ADMIN_ADD_MONEY': '管理员上分',
                    'ADMIN_REDUCE_MONEY': '管理员下分'
                }
            }
        }
    }
}
</script>

<style lang="less" scoped>
.user-fund-flow {
    .search-form-wrapper {
        margin-bottom: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 6px;
    }

    .table-wrapper {
        margin-top: 16px;
    }

    :deep(.ant-form-item) {
        margin-bottom: 16px;
    }

    :deep(.ant-table-thead > tr > th) {
        background: #fafafa;
        font-weight: 600;
    }

    :deep(.ant-table-tbody > tr:hover > td) {
        background: #e6f7ff;
    }

    .flow-records-modal {
        :deep(.ant-table-thead > tr > th) {
            background: #fafafa;
            font-weight: 600;
        }

        :deep(.ant-table-tbody > tr:hover > td) {
            background: #e6f7ff;
        }
    }
}
</style>
