<template>
    <div>
        <a-modal title="合同审核" :width="1000" :visible="userDialog" :footer="false" @cancel="userDialog = false">
            <a-descriptions bordered :title="currentDetails.signatureMsg ? '已签合同' : '未签合同'"
                :column="{ xxl: 3, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }">
                <a-descriptions-item label="签名图片">
                    <img :src="currentDetails.signatureMsg" alt="" style="width:140px;height: 70px;"
                        @click="previewImageFun(currentDetails.signatureMsg)">
                </a-descriptions-item>
            </a-descriptions>
            <div style="margin-top:20px;display:flex;justify-content: center;">
                <a-button type="danger" @click="postRejectSignature(currentDetails)">
                    驳回
                </a-button>
            </div>
        </a-modal>
        <a-modal title="确认驳回" :width="500" :visible="bohuidialog" :confirmLoading="bohuidialogloading"
            @ok="Okbohuidialog" @cancel="Cancelbohuidialog">
        </a-modal>

        <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible = false">
            <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
    </div>
</template>
<script>
import { userauthByAdmin, userDeleteSignature } from '@/api/home'
export default {
    components: {},
    props: {
        currentDetails: {
            type: Object,
            default: () => ({})
        },
        getinit: {
            type: Function,
            default: function () { }
        }
    },
    data() {
        return {
            userDialog: false,
            bohuidialog: false,
            bohuidialogloading: false,
            bohuiform: this.$form.createForm(this),
            previewVisible: false,
            previewImage: ''
        }
    },
    computed: {
        shouldShowRejectButton() {
            // 确保 currentDetails 存在且 signatureMsg 有效
            return this.currentDetails &&
                this.currentDetails.signatureMsg &&
                this.currentDetails.signatureMsg.length > 2
        }
    },
    methods: {
        previewImageFun(url) {
            if (url) {
                this.previewImage = url
                this.previewVisible = true
            }
        },

        postRejectSignature(val) {
            var that = this
            this.userDialog = false
            this.$confirm({
                title: '驳回开户合同',
                content: '确认驳回此用户的开户合同?',
                onOk() {
                    var data = {
                        userId: val.id
                    }
                    userDeleteSignature(data).then(res => {
                        if (res.status == 0) {
                            that.$message.success({ content: res.msg, duration: 2 })
                            that.getinit()
                        } else {
                            that.$message.error({ content: res.msg })
                        }
                    })
                },
                onCancel() {
                    console.log('Cancel')
                }
            })
        },
        Okbohuidialog() {
            const form = this.$refs.bohuiform.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    values.userId = this.currentDetails.id
                    values.state = 3
                    this.bohuidialogloading = true
                    userauthByAdmin(values).then(res => {
                        if (res.status == 0) {
                            this.bohuidialog = false
                            this.$message.success({ content: res.msg, duration: 2 })
                            form.resetFields()
                            this.getinit()
                        } else {
                            this.$message.error({ content: res.msg })
                        }
                        this.bohuidialogloading = false
                    })
                }
            })
        },
        Cancelbohuidialog() {
            this.bohuidialog = false
            const form = this.$refs.bohuiform.form
            form.resetFields()
        },
        gettongguo(val) {
            var data = {
                userId: this.currentDetails.id,
                state: val
            }
            userauthByAdmin(data).then(res => {
                if (res.status == 0) {
                    this.userDialog = false
                    this.getinit()
                } else {
                    this.$message.error({ content: res.msg })
                }
                this.userDialog = false
            })
        }
    }
}
</script>
