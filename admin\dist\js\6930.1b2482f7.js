(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[6930],{11334:function(e,t,a){"use strict";a.d(t,{FJ:function(){return p},IU:function(){return u},J5:function(){return m},Up:function(){return c},WU:function(){return d},WY:function(){return g},sE:function(){return s},xi:function(){return l},zX:function(){return f}});var r=a(16771),n=a(85070),i=a.n(n),o={getProductSetting:"/api/admin/getProductSetting.do",productupdate:"/admin/product/update.do",admingetSetting:"/api/admin/getSetting.do",setupdate:"/admin/set/update.do",admingetIndexSetting:"/api/admin/getIndexSetting.do",siteindexupdate:"/admin/site/index/update.do",admingetFuturesSetting:"/api/admin/getFuturesSetting.do",sitefuturesupdate:"/admin/site/futures/update.do",admingetSiteSpreadList:"/api/admin/getSiteSpreadList.do",adminaddSiteSpread:"/api/admin/addSiteSpread.do",adminupdateSiteSpread:"/api/admin/updateSiteSpread.do"};function s(e){return(0,r.Ay)({url:o.getProductSetting,method:"post",data:i().stringify(e)})}function l(e){return(0,r.Ay)({url:o.productupdate,method:"post",data:i().stringify(e)})}function d(e){return(0,r.Ay)({url:o.admingetSetting,method:"post",data:i().stringify(e)})}function u(e){return(0,r.Ay)({url:o.setupdate,method:"post",data:i().stringify(e)})}function c(e){return(0,r.Ay)({url:o.admingetIndexSetting,method:"post",data:i().stringify(e)})}function p(e){return(0,r.Ay)({url:o.siteindexupdate,method:"post",data:i().stringify(e)})}function g(e){return(0,r.Ay)({url:o.admingetSiteSpreadList,method:"post",data:i().stringify(e)})}function m(e){return(0,r.Ay)({url:o.adminaddSiteSpread,method:"post",data:i().stringify(e)})}function f(e){return(0,r.Ay)({url:o.adminupdateSiteSpread,method:"post",data:i().stringify(e)})}},34199:function(e,t,a){var r=1/0,n=9007199254740991,i="[object Arguments]",o="[object Function]",s="[object GeneratorFunction]",l="[object Symbol]",d="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,u="object"==typeof self&&self&&self.Object===Object&&self,c=d||u||Function("return this")();function p(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function g(e,t){var a=-1,r=e?e.length:0,n=Array(r);while(++a<r)n[a]=t(e[a],a,e);return n}function m(e,t){var a=-1,r=t.length,n=e.length;while(++a<r)e[n+a]=t[a];return e}var f=Object.prototype,h=f.hasOwnProperty,v=f.toString,y=c.Symbol,b=f.propertyIsEnumerable,S=y?y.isConcatSpreadable:void 0,C=Math.max;function w(e,t,a,r,n){var i=-1,o=e.length;a||(a=_),n||(n=[]);while(++i<o){var s=e[i];t>0&&a(s)?t>1?w(s,t-1,a,r,n):m(n,s):r||(n[n.length]=s)}return n}function x(e,t){return e=Object(e),U(e,t,(function(t,a){return a in e}))}function U(e,t,a){var r=-1,n=t.length,i={};while(++r<n){var o=t[r],s=e[o];a(s,o)&&(i[o]=s)}return i}function q(e,t){return t=C(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,n=C(a.length-t,0),i=Array(n);while(++r<n)i[r]=a[t+r];r=-1;var o=Array(t+1);while(++r<t)o[r]=a[r];return o[t]=i,p(e,this,o)}}function _(e){return P(e)||N(e)||!!(S&&e&&e[S])}function I(e){if("string"==typeof e||R(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}function N(e){return A(e)&&h.call(e,"callee")&&(!b.call(e,"callee")||v.call(e)==i)}var P=Array.isArray;function k(e){return null!=e&&z(e.length)&&!j(e)}function A(e){return F(e)&&k(e)}function j(e){var t=D(e)?v.call(e):"";return t==o||t==s}function z(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=n}function D(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function F(e){return!!e&&"object"==typeof e}function R(e){return"symbol"==typeof e||F(e)&&v.call(e)==l}var O=q((function(e,t){return null==e?{}:x(e,g(w(t,1),I))}));e.exports=O},36930:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return m}});var r=function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"table-page-search-wrapper"},[t("a-form",{attrs:{layout:"inline"}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"点差类型"}},[t("a-select",{attrs:{placeholder:"请选择点差类型"},model:{value:e.queryParam.typeName,callback:function(t){e.$set(e.queryParam,"typeName",t)},expression:"queryParam.typeName"}},[t("a-select-option",{attrs:{value:"涨跌幅"}},[e._v("涨跌幅")]),t("a-select-option",{attrs:{value:"成交额"}},[e._v("成交额")]),t("a-select-option",{attrs:{value:"开头收取"}},[e._v("开头收取")]),t("a-select-option",{attrs:{value:"低于收取"}},[e._v("低于收取")])],1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",[t("span",{staticClass:"table-page-search-submitButtons"},[t("a-button",{attrs:{icon:"redo"},on:{click:e.getqueryParam}},[e._v(" 重置")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){e.queryParam.pageNum=1,e.getlist()}}},[e._v("查询 ")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"plus"},on:{click:function(t){e.addUserdialog=!0,e.currentDetails=""}}},[e._v(" 添加点差")])],1)])],1)],1)],1)],1)]),t("a-card",{attrs:{bordered:!1}},[t("a-table",{attrs:{bordered:"",loading:e.loading,pagination:e.pagination,columns:e.columns,"data-source":e.datalist,rowKey:"id"},scopedSlots:e._u([{key:"zhi",fn:function(a,r){return t("span",{},[[t("div",[e._v(" ≤ X < ")])]],2)}},{key:"spreadRate",fn:function(a,r){return t("span",{},[[t("div",[t("span",{class:r.spreadRate<0?"greens":"reds"},[e._v(" "+e._s(r.spreadRate)+" ")])])]],2)}},{key:"action",fn:function(a,r){return[t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.geteditbaseCurrency(r)}},slot:"action"},[e._v("修改点差")])]}}])})],1),t("a-modal",{attrs:{title:e.currentDetails?"修改点差":"添加点差",width:500,visible:e.addUserdialog,confirmLoading:e.addUserDialogloading},on:{ok:e.OkaddUserdialog,cancel:e.CanceladdUserdialog}},[t("a-form",{ref:"addUserform",attrs:{form:e.addUserform}},[t("a-form-item",{attrs:{label:"点差类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["typeName",{rules:[{required:!0,message:"请选择点差类型"}]}],expression:"['typeName', { rules: [{ required: true, message: '请选择点差类型', }] }]"}],attrs:{placeholder:"请选择点差类型"}},[t("a-select-option",{attrs:{value:"涨跌幅"}},[e._v("涨跌幅")]),t("a-select-option",{attrs:{value:"成交额"}},[e._v("成交额")]),t("a-select-option",{attrs:{value:"开头收取"}},[e._v("开头收取")]),t("a-select-option",{attrs:{value:"低于收取"}},[e._v("低于收取")])],1)],1),t("a-form-item",{attrs:{label:"单位",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["unit",{rules:[{required:!0,message:"请输入单位"}]}],expression:"['unit', { rules: [{ required: true, message: '请输入单位', }] }]"}],attrs:{placeholder:"请输入单位"}})],1),t("a-form-item",{attrs:{label:"开始区间",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["startInterval",{rules:[{required:!0,message:"请输入开始区间"}]}],expression:"['startInterval', { rules: [{ required: true, message: '请输入开始区间', }] }]"}],attrs:{placeholder:"请输入开始区间"}})],1),t("a-form-item",{attrs:{label:"结束区间",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["endInterval",{rules:[{required:!0,message:"请输入结束区间"}]}],expression:"['endInterval', { rules: [{ required: true, message: '请输入结束区间', }] }]"}],attrs:{placeholder:"请输入结束区间"}})],1),t("a-form-item",{attrs:{label:"点差费率",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["spreadRate",{rules:[{required:!0,message:"请输入点差费率"}]}],expression:"['spreadRate', { rules: [{ required: true, message: '请输入点差费率', }] }]"}],attrs:{placeholder:"请输入点差费率"}})],1)],1)],1)],1)},n=[],i=(a(27296),a(89077),a(11334)),o=a(46279),s=a.n(o),l=a(34199),d=a.n(l),u={name:"Spreadsetting",data:function(){var e=this;return{columns:[{title:"类型名称",dataIndex:"typeName",align:"center"},{title:"单位",dataIndex:"unit",align:"center"},{title:"开始区间",dataIndex:"startInterval",align:"center"},{title:"值",dataIndex:"zhi",align:"center",scopedSlots:{customRender:"zhi"}},{title:"结束区间",dataIndex:"endInterval",align:"center"},{title:"点差费率",dataIndex:"spreadRate",align:"center",scopedSlots:{customRender:"spreadRate"}},{title:"添加时间",dataIndex:"addTime",align:"center",width:180,customRender:function(e,t,a){return e?s()(e).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,typeName:void 0},datalist:[],addUserdialog:!1,addUserDialogloading:!1,addUserform:this.$form.createForm(this),labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},fields:["typeName","unit","startInterval","endInterval","spreadRate"],currentDetails:""}},created:function(){this.getlist()},methods:{geteditbaseCurrency:function(e){var t=this;this.currentDetails=e,this.addUserdialog=!0,this.fields.forEach((function(e){return t.addUserform.getFieldDecorator(e)})),this.addUserform.setFieldsValue(d()(e,this.fields))},CanceladdUserdialog:function(){this.addUserdialog=!1;var e=this.$refs.addUserform.form;e.resetFields()},OkaddUserdialog:function(){var e=this,t=this.$refs.addUserform.form;t.validateFields((function(a,r){a||(e.addUserDialogloading=!0,""!=e.currentDetails?(r.id=e.currentDetails.id,(0,i.zX)(r).then((function(a){0==a.status?(e.addUserdialog=!1,e.$message.success({content:"修改成功",duration:2}),t.resetFields(),e.getlist()):e.$message.error({content:a.msg}),e.addUserDialogloading=!1}))):(r.id=0,(0,i.J5)(r).then((function(a){0==a.status?(e.addUserdialog=!1,e.$message.success({content:"添加成功",duration:2}),t.resetFields(),e.getlist()):e.$message.error({content:a.msg}),e.addUserDialogloading=!1}))))}))},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,typeName:void 0}},getlist:function(){var e=this;this.loading=!0,(0,i.WY)(this.queryParam).then((function(t){e.datalist=t.data.data.list,e.pagination.total=t.data.data.total,e.loading=!1}))},onPageChange:function(e,t){this.queryParam.pageNum=e,this.getlist()},onSizeChange:function(e,t){this.queryParam.pageNum=e,this.queryParam.pageSize=t,this.getlist()}}},c=u,p=a(6367),g=(0,p.A)(c,r,n,!1,null,"fd779d06",null),m=g.exports}}]);