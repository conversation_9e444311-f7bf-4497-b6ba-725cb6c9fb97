<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-card :bordered="false">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :md="12" :lg="6" :sm="24">
                <a-form-item label="用户筛选">
                  <a-select
                    v-model="queryParam.accountType"
                    placeholder="请选择用户类型"
                    :default-value="{ key: '0' }"
                    @change="accountTypeChangeEvent">
                    <a-select-option :value="0">真实用户</a-select-option>
                    <a-select-option :value="1">模拟用户</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :md="12" :lg="6" :sm="24">
                <a-form-item label="充值状态">
                  <a-select v-model="queryParam.state" placeholder="请选择充值状态">
                    <a-select-option :value="0">审核中</a-select-option>
                    <a-select-option :value="1">入金成功</a-select-option>
                    <a-select-option :value="2">入金失败</a-select-option>
                    <a-select-option :value="3">入金取消</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :lg="6" :sm="24">
                <a-form-item label="下级代理">
                  <a-select
                    v-model="queryParam.agentId"
                    placeholder="请选择下级代理"
                    @focus="getagentlist"
                    :loading="agentloading"
                    @change="agentIdChangeEvent">
                    <a-select-option
                      v-for="(item, index) in agentlist"
                      :key="index"
                      :value="item.id">
                      {{ item.agentName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="12" :lg="6" :sm="24">
                <a-form-item label="用户Id">
                  <a-input v-model="queryParam.userId" style="width: 100%" placeholder="请输入用户Id" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :lg="6" :sm="24">
                <a-form-item label="真实姓名">
                  <a-input v-model="queryParam.realName" style="width: 100%" placeholder="请输入真实姓名" />
                </a-form-item>
              </a-col>
              <a-col :md="12" :lg="6" :sm="24">
                <a-form-item label="手机号">
                  <a-input v-model="queryParam.phone" style="width: 100%" placeholder="请输入手机号" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="48">
              <a-col :md="12" :lg="8" :sm="24">
                <a-form-item label="支付时间">
                  <a-range-picker
                    style="width: 100%"
                    v-model="times"
                    @change="onChangeRangeDate"
                    format="YYYY-MM-DD HH:mm:ss"
                    :show-time="{ format: 'HH:mm:ss' }"
                    :ranges="dateRanges">
                  </a-range-picker>
                </a-form-item>
              </a-col>

              <a-col :md="12" :lg="8" :sm="24">
                <a-form-item>
                  <span class="table-page-search-submitButtons">
                    <a-button @click="getqueryParam" icon="redo">
                      重置</a-button>
                    <a-button
                      type="primary"
                      icon="search"
                      style="margin-left: 8px"
                      @click="queryParam.pageNum = 1, getlist()">查询
                    </a-button>
                    <a-button
                      type="primary"
                      icon="search"
                      style="margin-left: 8px"
                      @click="queryToday()">查询今日</a-button>
                    <a-button
                      type="primary"
                      icon="search"
                      style="margin-left: 8px"
                      @click="queryYesterday()">查询昨日</a-button>

                    <a-button
                      type="primary"
                      icon="plus"
                      style="margin-left: 8px"
                      @click="addOrderdialog = true">创建充值订单
                    </a-button>
                    <a-button
                      type="primary"
                      icon="vertical-align-bottom"
                      style="margin-left: 8px"
                      @click="getexport">导出搜索数据
                    </a-button>
                  </span>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <div>{{ queryParam.accountType == 1 ? '模拟用户' : '真实用户' }} - 今日充值：{{ totalResult.todayRechargeAmount }}，{{
        queryParam.accountType == 1 ? '模拟用户' : '真实用户' }} - 总充值：{{ totalResult.totalRechargeAmount }}，{{
        queryParam.accountType == 1 ? '模拟用户' : '真实用户' }} - 今日首充人数：{{ totalResult.todayFirstRechargeNums }}，{{
        queryParam.accountType == 1 ? '模拟用户' : '真实用户' }} - 今日首充金额：{{ totalResult.todayFirstRechargeAmount }}
      </div>
      <div style="margin-top: 10px;">
        <span>{{ queryParam.accountType == 1 ? '模拟用户' : '真实用户' }} - </span>
        <span class="greens">总充值金额：{{ statisticsData.totalRechargeAmount }}</span>，
        <span class="greens">充值成功金额：{{ statisticsData.rechargeSuccessAmount }}</span>，
        <span class="reds">充值失败金额：{{ statisticsData.rechargeFailAmount }}</span>，
        <span style="color: #1890ff;">后台充值金额：{{ statisticsData.backendRechargeSuccessAmount }}</span>，
        <span style="color: #722ed1;">第三方充值金额：{{ statisticsData.thirdRechargeSuccessAmount }}</span>
      </div>
      <a-table
        bordered
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data-source="datalist"
        rowKey="id"
        style="margin-top:10px;">
        <span slot="payChannel" slot-scope="text,record">
          <template>
            <div>
              <a-tag
                :color="record.payChannel == 0 ? 'blue' : record.payChannel == 1 ? 'orange' : 'cyan'">
                {{ record.payChannel == 0 ? '支付宝' : record.payChannel == 1 ? '对公转账' : '现金转账'
                }}
              </a-tag>
            </div>
          </template>
        </span>
        <span slot="orderStatus" slot-scope="text,record">
          <template>
            <div>
              <a-tag
                :color="record.orderStatus == 0 ? 'blue' : record.orderStatus == 1 ? 'green' : record.orderStatus == 2 ? 'red' : 'orange'">
                {{ record.orderStatus == 0 ? '审核中' : record.orderStatus == 1 ? '充值成功' :
                  record.orderStatus == 2 ? '充值失败' : '订单取消'
                }}
              </a-tag>
            </div>
          </template>
        </span>

        <template slot="action" slot-scope="text,record">
          <a
            slot="action"
            href="javascript:;"
            @click="getEditorder(record.id)"
            v-if="record.orderStatus == 0">修改状态</a>
          <a-divider type="vertical" v-if="record.orderStatus == 0" />
          <a slot="action" href="javascript:;" @click="getDelorder(record.id)">删除订单</a>
        </template>
      </a-table>
    </a-card>
    <a-modal
      title="修改订单状态"
      :width="500"
      :visible="editOrderdialog"
      :confirmLoading="editOrderDialogloading"
      @ok="OkeditOrderdialog"
      @cancel="CanceleditOrderdialog">
      <a-form :form="editOrderform" ref="editOrderform">
        <a-form-item label="订单ID">
          <a-input
            v-decorator="['chargeId', { rules: [{ type: 'number', required: true, message: '请输入锁仓原因！', }] }]"
            disabled />
        </a-form-item>
        <a-form-item label="充值状态">
          <a-select
            placeholder="请选择充值状态"
            v-decorator="['state', { rules: [{ required: true, message: '请选择充值状态', }] }]">
            <a-select-option value="1">充值成功</a-select-option>
            <a-select-option value="2">充值失败</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="快捷单号" v-if="editOrderform.getFieldValue('state') === '1'">
          <a-input
            placeholder="请输入快捷单号"
            v-decorator="['thirdOrderNo']" />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal
      title="创建充值订单"
      :width="500"
      :visible="addOrderdialog"
      :confirmLoading="addOrderDialogloading"
      @ok="OkaddOrderdialog"
      @cancel="CanceladdOrderdialog">
      <a-form :form="addOrderform" ref="addOrderform">
        <a-form-item label="用户ID">
          <a-input
            placeholder="请输入用户ID"
            v-decorator="['userId', { rules: [{ required: true, message: '请输入用户ID！', }] }]" />
        </a-form-item>
        <a-form-item label="充值金额">
          <a-input
            placeholder="请输入充值金额"
            v-decorator="['amt', { rules: [{ required: true, message: '请输入充值金额！', }] }]" />
        </a-form-item>
        <a-form-item label="充值状态">
          <a-select
            placeholder="请选择充值状态"
            v-decorator="['state', { rules: [{ required: true, message: '请选择充值状态', }] }]">
            <a-select-option value="0">充值中</a-select-option>
            <a-select-option value="1">充值成功</a-select-option>
            <a-select-option value="2">充值失败</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="充值渠道">
          <a-select
            placeholder="请选择充值渠道"
            v-decorator="['payChannel', { rules: [{ required: true, message: '请选择充值渠道', }] }]">
            <a-select-option value="0">支付宝</a-select-option>
            <a-select-option value="1">对公转账</a-select-option>
            <a-select-option value="2">现金转账</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </page-header-wrapper>
</template>
<script>
import {
    rechargelist,
    rechargedel,
    rechargeupdateState,
    rechargecreateOrder,
    rechargeexport,
    rechargeCountRechargeAmount,
    rechargeStatistics
} from '@/api/capital'
import { nextagent } from '@/api/home'
import pick from 'lodash.pick'
import fileDownload from 'js-file-download'
import moment from 'moment'
import { remove } from 'nprogress'
export default {
    name: 'Rechargelist',
    data () {
        return {
            dateRanges: {
                '今天': [moment().startOf('day'), moment().endOf('day')],
                '昨天': [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
                '本周': [moment().startOf('week'), moment().endOf('week')],
                '本月': [moment().startOf('month'), moment().endOf('month')]
            },
            columns: [
                {
                    title: '用户名称（ID）',
                    dataIndex: 'nickName',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return `${row.nickName}（${row.userId}）`
                    }
                },
                {
                    title: '订单ID',
                    dataIndex: 'id',
                    align: 'center'
                },
                {
                    title: '代理用户',
                    dataIndex: 'agentName',
                    align: 'center'
                },
                {
                    title: '订单号',
                    dataIndex: 'orderSn',
                    align: 'center'
                },
                {
                    title: '快捷单号',
                    dataIndex: 'paySn',
                    align: 'center'
                },
                {
                    title: '充值渠道',
                    dataIndex: 'payChannel',
                    align: 'center',
                    scopedSlots: { customRender: 'payChannel' }
                },
                {
                    title: '充值金额',
                    dataIndex: 'payAmt',
                    align: 'center'
                },
                {
                    title: '状态',
                    dataIndex: 'orderStatus',
                    align: 'center',
                    scopedSlots: { customRender: 'orderStatus' }
                },
                {
                    title: '申请时间',
                    dataIndex: 'addTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                },
                {
                    title: '支付时间',
                    dataIndex: 'payTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                },

                {
                    title: '备注',
                    dataIndex: 'remark',
                    align: 'center'
                },
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    align: 'center',
                    customRender: (text, row, index) => {
                        if (!text) return ''
                        const phoneStr = String(text)
                        return phoneStr.substr(0, 3) + '****' + phoneStr.substr(7)
                    }
                },
                {
                    title: '操作者',
                    dataIndex: 'operator',
                    align: 'center'
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'center',
                    width: 180,
                    scopedSlots: { customRender: 'action' }
                }
            ],
            // 表头
            pagination: {
                total: 0,
                pageSize: 10, // 每页中显示10条数据
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize), // 改变每页数量时更新显示
                onChange: (page, pageSize) => this.onPageChange(page, pageSize), // 点击页码事件
                showTotal: (total) => `共有 ${total} 条数据` // 分页中显示总的数据
            },
            loading: false,
            queryParam: {
                accountType: undefined,
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                state: undefined,
                userId: '',
                realName: '',
                beginTime: '',
                endTime: '',
                phone: ''
            },
            datalist: [],
            agentlist: [],
            agentloading: false,
            times: [],
            editOrderform: this.$form.createForm(this),
            editOrderdialog: false,
            editOrderDialogloading: false,
            fields: ['chargeId', 'state', 'thirdOrderNo'],

            addOrderform: this.$form.createForm(this),
            addOrderdialog: false,
            addOrderDialogloading: false,
            agentqueryParam: {
                pageNum: 1,
                pageSize: 100
            },
            totalResult: {},
            statisticsData: {
                totalRechargeAmount: 0,
                rechargeSuccessAmount: 0,
                rechargeFailAmount: 0,
                backendRechargeSuccessAmount: 0,
                thirdRechargeSuccessAmount: 0
            }
        }
    },
    created () {
        this.getlist()
        this.rechargeCountRechargeAmountEvent()
        this.getRechargeStatistics()
    },
    methods: {
        queryToday () {
            this.times = [moment().startOf('day'), moment().endOf('day')]
            this.queryParam.beginTime = moment(this.times[0]).format('YYYY-MM-DD HH:mm:ss')
            this.queryParam.endTime = moment(this.times[1]).format('YYYY-MM-DD HH:mm:ss')
            this.queryParam.pageNum = 1
            this.getlist()
            this.getRechargeStatistics()
        },
        queryYesterday () {
            // 获取昨天的日期
            const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD')
            // 设置时间范围为昨天的 00:00:00 到 23:59:59
            this.times = [
                moment(yesterday + ' 00:00:00', 'YYYY-MM-DD HH:mm:ss'),
                moment(yesterday + ' 23:59:59', 'YYYY-MM-DD HH:mm:ss')
            ]
            this.queryParam.beginTime = moment(this.times[0]).format('YYYY-MM-DD HH:mm:ss')
            this.queryParam.endTime = moment(this.times[1]).format('YYYY-MM-DD HH:mm:ss')
            this.queryParam.pageNum = 1
            this.getlist()
            this.getRechargeStatistics()
        },
        rechargeCountRechargeAmountEvent () {
            rechargeCountRechargeAmount({
                accountType: this.queryParam.accountType,
                agentId: this.queryParam.agentId
            }).then((res) => {
                this.totalResult = res.data
            })
        },
        getRechargeStatistics () {
            const { pageNum, pageSize, ...queryParams } = this.queryParam
            rechargeStatistics(queryParams).then((res) => {
                if (res.status === 0 && res.data) {
                    this.statisticsData = res.data
                }
            })
        },
        accountTypeChangeEvent () {
            this.rechargeCountRechargeAmountEvent()
            this.getRechargeStatistics()
        },
        agentIdChangeEvent (e) {
            this.rechargeCountRechargeAmountEvent()
            this.getRechargeStatistics()
        },
        getexport () {
            rechargeexport(this.queryParam).then((res) => {
                fileDownload(res, '充值列表.xls')
            })
        },
        // 新增充值订单弹窗
        CanceladdOrderdialog () {
            this.addOrderdialog = false
            const form = this.$refs.addOrderform.form
            form.resetFields()
        },
        // 新增充值订单
        OkaddOrderdialog () {
            const form = this.$refs.addOrderform.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    this.addOrderDialogloading = true
                    rechargecreateOrder(values)
                        .then((res) => {
                            if (res.status == 0) {
                                this.addOrderdialog = false
                                this.$message.success({ content: res.msg, duration: 2 })
                                form.resetFields()
                                this.getlist()
                            } else {
                                this.$message.error({ content: res.msg })
                            }
                            this.addOrderDialogloading = false
                        })
                        .catch((error) => {
                            reject(error)
                        })
                }
            })
        },
        // 点击修改充值状态
        getEditorder (val) {
            this.editOrderdialog = true
            this.fields.forEach((v) => this.editOrderform.getFieldDecorator(v))
            this.editOrderform.setFieldsValue(pick({ chargeId: val }, this.fields))
        },
        // 开始更改充值状态
        OkeditOrderdialog () {
            const form = this.$refs.editOrderform.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    this.editOrderDialogloading = true
                    rechargeupdateState(values)
                        .then((res) => {
                            if (res.status == 0) {
                                this.editOrderdialog = false
                                this.$message.success({ content: res.msg, duration: 2 })
                                form.resetFields()
                                this.getlist()
                            } else {
                                this.$message.error({ content: res.msg })
                            }
                            this.editOrderDialogloading = false
                        })
                        .catch((error) => {
                            reject(error)
                        })
                }
            })
        },
        // 取消充值状态修改
        CanceleditOrderdialog () {
            this.editOrderdialog = false
            const form = this.$refs.editOrderform.form
            form.resetFields()
        },
        // 删除充值订单
        getDelorder (val) {
            var that = this
            this.$confirm({
                title: '提示',
                content: '确认删除充值订单吗？此操作不可恢复',
                onOk () {
                    var data = {
                        cId: val
                    }
                    rechargedel(data).then((res) => {
                        if (res.status == 0) {
                            that.$message.success({ content: res.msg, duration: 2 })
                            that.getlist()
                        } else {
                            that.$message.error({ content: res.msg })
                        }
                    })
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        onChangeRangeDate (value, dateString) {
            // 检查是否有选择日期
            if (value && value.length === 2 && value[0] && value[1]) {
                // 使用 moment 对象直接设置时间
                // 开始时间设置为当天的 00:00:00
                const startDate = value[0].clone().startOf('day')
                // 结束时间设置为当天的 23:59:59
                const endDate = value[1].clone().endOf('day')

                // 更新 times 数组
                this.times = [startDate, endDate]

                // 更新查询参数
                this.queryParam.beginTime = startDate.format('YYYY-MM-DD HH:mm:ss')
                this.queryParam.endTime = endDate.format('YYYY-MM-DD HH:mm:ss')

                console.log('设置的时间范围：', this.queryParam.beginTime, this.queryParam.endTime)

                // 更新统计数据
                this.getRechargeStatistics()
            } else {
                // 如果没有选择日期，则清空时间范围
                this.queryParam.beginTime = ''
                this.queryParam.endTime = ''
                this.times = []
            }
        },
        getqueryParam () {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                state: undefined,
                userId: '',
                realName: '',
                beginTime: '',
                endTime: '',
                phone: ''
            }
            this.times = []
        },
        getagentlist () {
            var that = this
            this.agentloading = true
            nextagent(this.agentqueryParam).then((res) => {
                this.agentlist = res.data.list
                setTimeout(() => {
                    that.agentloading = false
                }, 500)
            })
        },
        getlist () {
            var that = this
            this.loading = true
            rechargelist(this.queryParam).then((res) => {
                this.datalist = res.data.list
                this.pagination.total = res.data.total
                setTimeout(() => {
                    that.loading = false
                }, 500)
            })
            this.getRechargeStatistics()
        },
        onPageChange (page, pageSize) {
            this.queryParam.pageNum = page
            this.getlist()
        },
        onSizeChange (current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.getlist()
        }
    }
}
</script>
<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
</style>
