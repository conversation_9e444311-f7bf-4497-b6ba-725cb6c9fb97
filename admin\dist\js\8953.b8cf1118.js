"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[8953],{46101:function(t,e,a){a.d(e,{AJ:function(){return d},DI:function(){return x},Gp:function(){return T},KD:function(){return h},Ks:function(){return P},N4:function(){return s},NW:function(){return _},O3:function(){return c},S2:function(){return f},Zb:function(){return v},_m:function(){return k},dN:function(){return y},dX:function(){return b},hA:function(){return I},iK:function(){return l},iz:function(){return m},jP:function(){return u},xH:function(){return p},xh:function(){return g},zy:function(){return S}});a(27296),a(24362);var i=a(16771),n=a(85070),o=a.n(n),r={positionlist:"/admin/position/list.do",indexpositionlist:"/admin/index/position/list.do",futurespositionlist:"/admin/futures/position/list.do",positionlock:"/admin/position/lock.do",positionsell:"/admin/position/sell.do",positiondel:"/admin/position/del.do",indexpositionlock:"/admin/index/position/lock.do",indexpositionsell:"/admin/index/position/sell.do",indexpositiondel:"/admin/index/position/del.do",futurespositionlock:"/admin/futures/position/lock.do",futurespositionsell:"/admin/futures/position/sell.do",futurespositiondel:"/admin/futures/position/del.do",userdetail:"/admin/user/detail.do",stockgetSingleStock:"/api/stock/getSingleStock.do",positioncreate:"/admin/position/create.do",buyOneTread:"/admin/position/updateStatus.do",buyAllTread:"admin/position/batchAudit.do",canCelOneTread:"/admin/position/cancelDz.do",oneClickTransaction:"/admin/position/oneClickTransaction.do",virtualOneClickTransaction:"/admin/position/virtualOneClickTransaction.do",cancelOrder:"/admin/position/cancelOrder.do"};function s(t){return(0,i.Ay)({url:r.cancelOrder,method:"post",data:o().stringify(t)})}function c(t){return(0,i.Ay)({url:r.positionlist,method:"post",data:o().stringify(t)})}function l(t){return(0,i.Ay)({url:r.indexpositionlist,method:"post",data:o().stringify(t)})}function d(t){return(0,i.Ay)({url:r.futurespositionlist,method:"post",data:o().stringify(t)})}function u(t){return(0,i.Ay)({url:r.positionlock,method:"post",data:o().stringify(t)})}function m(t){return(0,i.Ay)({url:r.positionsell,method:"post",data:o().stringify(t)})}function g(t){return(0,i.Ay)({url:r.buyOneTread,method:"post",data:o().stringify(t)})}function p(t){return(0,i.Ay)({url:r.oneClickTransaction,method:"post",data:o().stringify(t)})}function f(){return(0,i.Ay)({url:r.virtualOneClickTransaction,method:"post"})}function h(t){return(0,i.Ay)({url:r.buyAllTread+"?"+t.toString(),method:"get",data:t})}function v(t){return(0,i.Ay)({url:r.positiondel,method:"post",data:o().stringify(t)})}function y(t){return(0,i.Ay)({url:r.indexpositionlock,method:"post",data:o().stringify(t)})}function k(t){return(0,i.Ay)({url:r.indexpositionsell,method:"post",data:o().stringify(t)})}function _(t){return(0,i.Ay)({url:r.indexpositiondel,method:"post",data:o().stringify(t)})}function b(t){return(0,i.Ay)({url:r.futurespositionlock,method:"post",data:o().stringify(t)})}function P(t){return(0,i.Ay)({url:r.futurespositionsell,method:"post",data:o().stringify(t)})}function I(t){return(0,i.Ay)({url:r.futurespositiondel,method:"post",data:o().stringify(t)})}function S(t){return(0,i.Ay)({url:r.userdetail,method:"post",data:o().stringify(t)})}function x(t){return(0,i.Ay)({url:r.stockgetSingleStock,method:"post",data:o().stringify(t)})}function T(t){return(0,i.Ay)({url:r.positioncreate,method:"post",data:o().stringify(t)})}},88953:function(t,e,a){a.r(e),a.d(e,{default:function(){return et}});var i=function(){var t=this,e=t._self._c;return e("page-header-wrapper",[e("a-card",[e("a-tabs",{attrs:{"default-active-key":"0"},on:{tabClick:t.gettabchange}},[e("a-tab-pane",{key:"0",attrs:{tab:"股票委托单",forceRender:""}},[e("financingHold",{ref:"financingHold"})],1),e("a-tab-pane",{key:"1",attrs:{tab:"股票持仓单",forceRender:""}},[e("financingHold",{ref:"financingHold"})],1),e("a-tab-pane",{key:"2",attrs:{tab:"股票平仓单",forceRender:""}},[e("financingFlat",{ref:"financingFlats"})],1),e("a-tab-pane",{key:"3",attrs:{tab:"已取消的订单",forceRender:""}},[e("financingFlatCancel",{ref:"financingCancel"})],1)],1)],1)],1)},n=[],o=(a(47875),function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户名"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户名"},model:{value:t.queryParam.nickName,callback:function(e){t.$set(t.queryParam,"nickName",e)},expression:"queryParam.nickName"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"股票代码"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入股票代码"},model:{value:t.queryParam.stockCode,callback:function(e){t.$set(t.queryParam,"stockCode",e)},expression:"queryParam.stockCode"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"股票名称"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入股票名称"},model:{value:t.queryParam.stockName,callback:function(e){t.$set(t.queryParam,"stockName",e)},expression:"queryParam.stockName"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(e){return t.allChecked()}}},[t._v("全选")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(e){return t.transaction()}}},[t._v("一键成交")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(e){return t.virtualOneClickTransactionEvent()}}},[t._v("模拟持仓一键成交")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"selection",fn:function(a,i,n){return[e("a-checkbox",{attrs:{value:i.id,checked:t.chooseList.indexOf(i.id)>-1},on:{change:function(e){return t.onChange(i.id,e)}}})]}},{key:"stockName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.stockName))]),3!=i.positionType?e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==i.stockPlate?"科创":i.stockPlate?i.stockPlate:"股票")+" ")]):e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" 大宗 ")]),e("p",[t._v("("+t._s(i.stockCode)+")")])],1)]],2)}},{key:"agentName",fn:function(a,i){return e("span",{},[[e("div",[e("span",[t._v(t._s(i.agentName))]),e("p",[t._v("("+t._s(i.agentCode)+")")])])]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"status",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.status?"blue":"red"}},[t._v(" "+t._s(1!=i.status?"未成交":"已经成交")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"now_price",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:Number(i.now_price)-i.buyOrderPrice<0?"greens":Number(i.now_price)-i.buyOrderPrice>0?"reds":""},[t._v(" "+t._s(i.now_price)+" ")])])]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[0==i.isLock?e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){t.Lockvisibledialog=!0,t.clickpositionId=i.id}},slot:"action"},[t._v("锁仓")]):e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getLockopen(i.id)}},slot:"action"},[t._v("解锁")]),e("a-divider",{attrs:{type:"vertical"}}),e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getCompulsoryclosing(i.positionSn)}},slot:"action"},[t._v("强制平仓")]),1!=i.status?e("a-divider",{attrs:{type:"vertical"}}):t._e(),1!=i.status?e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getBuyFinish(i)}},slot:"action"},[t._v("点击成交")]):t._e(),0==i.buyNum?e("a-divider",{attrs:{type:"vertical"}}):t._e(),0==i.buyNum?e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.cancelOrderEvent(i)}},slot:"action"},[t._v("撤单")]):t._e()]}}])}),e("a-modal",{attrs:{title:"锁仓",width:640,visible:t.Lockvisibledialog,confirmLoading:t.Lockvisibleloading},on:{ok:t.getDialogok,cancel:t.handleCancel}},[e("a-form",{ref:"createModal",attrs:{form:t.Lockvisibleform}},[e("a-form-item",[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["lockMsg",{rules:[{required:!0,message:"请输入锁仓原因！",whitespace:!0}]}],expression:"['lockMsg', { rules: [{ required: true, message: '请输入锁仓原因！', whitespace: true }] }]"}],attrs:{placeholder:"请输入锁仓原因！"}})],1)],1)],1),e("a-modal",{attrs:{title:"购买成交",width:640,visible:t.ConfirmTreadDialog,confirmLoading:t.ConfirmTreadDialogding},on:{ok:t.getTradeDialogok,cancel:t.handleTreadCancel}},[e("a-form",{ref:"createModal",attrs:{form:t.ConfirmTreadform}},[e("a-form-item",[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"成交比例"}},[e("a-select",{attrs:{placeholder:"请选择成交比例"},model:{value:t.buyRatio,callback:function(e){t.buyRatio=e},expression:"buyRatio"}},[e("a-select-option",{attrs:{value:10}},[t._v("10%")]),e("a-select-option",{attrs:{value:20}},[t._v("20%")]),e("a-select-option",{attrs:{value:30}},[t._v("30%")]),e("a-select-option",{attrs:{value:40}},[t._v("40%")]),e("a-select-option",{attrs:{value:50}},[t._v("50%")]),e("a-select-option",{attrs:{value:60}},[t._v("60%")]),e("a-select-option",{attrs:{value:70}},[t._v("70%")]),e("a-select-option",{attrs:{value:80}},[t._v("80%")]),e("a-select-option",{attrs:{value:90}},[t._v("90%")]),e("a-select-option",{attrs:{value:100}},[t._v("100%")])],1)],1)],1)],1)],1)],1),e("a-modal",{attrs:{title:"购买成交",width:640,visible:t.ConfirmTreadDialogAll,confirmLoading:t.ConfirmTreadDialogdingAll},on:{ok:t.getTradeDialogokAll,cancel:t.handleTreadCancelAll}},[e("a-form",{ref:"createModal",attrs:{form:t.ConfirmTreadform}},[e("a-form-item",[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"成交比例"}},[e("a-select",{attrs:{placeholder:"请选择成交比例"},model:{value:t.buyRatio,callback:function(e){t.buyRatio=e},expression:"buyRatio"}},[e("a-select-option",{attrs:{value:10}},[t._v("10%")]),e("a-select-option",{attrs:{value:20}},[t._v("20%")]),e("a-select-option",{attrs:{value:30}},[t._v("30%")]),e("a-select-option",{attrs:{value:40}},[t._v("40%")]),e("a-select-option",{attrs:{value:50}},[t._v("50%")]),e("a-select-option",{attrs:{value:60}},[t._v("60%")]),e("a-select-option",{attrs:{value:70}},[t._v("70%")]),e("a-select-option",{attrs:{value:80}},[t._v("80%")]),e("a-select-option",{attrs:{value:90}},[t._v("90%")]),e("a-select-option",{attrs:{value:100}},[t._v("100%")])],1)],1)],1)],1)],1)],1)],1)}),r=[],s=(a(27609),a(96124),a(32429),a(79207),a(77913),a(76817),a(27296),a(24362),a(6866),a(83129),a(89077),a(72),a(24723),a(46101)),c=a(18697),l=a(46279),d=a.n(l),u={name:"financinghold",data:function(){var t=this;return{columns:[{title:"选择",dataIndex:"selection",align:"center",width:60,scopedSlots:{customRender:"selection"}},{title:"融资名称",dataIndex:"stockName",align:"center",width:180,scopedSlots:{customRender:"stockName"}},{title:"上级代理",dataIndex:"agentName",align:"center",width:180,scopedSlots:{customRender:"agentName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"是否成交",dataIndex:"status",align:"center",scopedSlots:{customRender:"status"}},{title:"用户名称（ID）",dataIndex:"nickName",align:"center",customRender:function(t,e,a){return"".concat(e.nickName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入价",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"现价",dataIndex:"now_price",align:"center",scopedSlots:{customRender:"now_price"}},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"总数量（股）",dataIndex:"orderNum",align:"center"},{title:"总市值",dataIndex:"orderTotalPrice",align:"center"},{title:"成交（股）",dataIndex:"buyNum",align:"center"},{title:"成交金额",dataIndex:"buyPrice",align:"center"},{title:"未成交（股）",dataIndex:"restNUm",align:"center"},{title:"未成交金额",dataIndex:"restPrice",align:"center"},{title:"未成交是否撤单",dataIndex:"backStatus",align:"center",customRender:function(t,e,a){return 0==t?"未撤销":"已撤销"}},{title:"杠杆倍数",dataIndex:"orderLever",align:"center"},{title:"手续费",dataIndex:"orderFee",align:"center"},{title:"印花税",dataIndex:"orderSpread",align:"center"},{title:"留仓费",dataIndex:"orderStayFee",align:"center"},{title:"留仓天数",dataIndex:"orderStayDays",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:250,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0,stockCode:"",stockName:"",nickName:""},datalist:[],agentlist:[],agentloading:!1,Lockvisibledialog:!1,Lockvisibleloading:!1,ConfirmTreadDialog:!1,ConfirmTreadDialogding:!1,ConfirmTreadDialogAll:!1,ConfirmTreadDialogdingAll:!1,ConfirmTreadform:this.$form.createForm(this),Lockvisibleform:this.$form.createForm(this),clickpositionId:"",buyRatio:100,agentqueryParam:{pageNum:1,pageSize:100},chooseList:[],oneTreadData:{},isAll:!1,ids:[]}},created:function(){this.getlist()},methods:{allChecked:function(){this.chooseList=[];for(var t=0;t<this.datalist.length;t++)this.chooseList.push(this.datalist[t].id)},virtualOneClickTransactionEvent:function(){var t=this;(0,s.S2)().then((function(e){1==e.status?t.$message.error({content:e.msg}):(t.$message.success({content:e.data,duration:2}),t.getlist())}))},transaction:function(){var t=this;this.chooseList.length<=0?this.$message.error({content:"请选择ID"}):(0,s.xH)({ids:this.chooseList.join(",")}).then((function(e){1==e.status?t.$message.error({content:e.msg}):(t.$message.success({content:e.data,duration:2}),t.getlist())}))},getCompulsoryclosing:function(t){var e=this;this.$confirm({title:"提示",content:"确认要强制平仓吗?",onOk:function(){var a={positionSn:t};(0,s.iz)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.success({content:t.msg,duration:2})})).catch((function(t){}))},onCancel:function(){console.log("Cancel"),e.$message.success({content:"平仓失败",duration:2})}})},getBuyFinish:function(t){this.oneTreadData={id:t.id,status:1,userId:t.userId},this.ConfirmTreadDialog=!0},cancelOrderEvent:function(t){var e=this;this.$confirm({title:"提示",content:"确认撤单吗?",onOk:function(){var a={id:t.id,userId:t.userId};(0,s.N4)(a).then((function(t){0==t.status?(e.$message.success({content:t.data,duration:2}),e.getlist()):e.$message.success({content:t.msg,duration:2})})).catch((function(t){}))},onCancel:function(){console.log("Cancel")}})},getAllFinish:function(t){!t&&this.chooseList.length<1?this.$message.error({content:"请先选择需要成交的条目"}):(this.isAll=t,this.ConfirmTreadDialogAll=!0)},onChange:function(t,e){if(e.target.checked)this.chooseList.push(t);else{var a=this.chooseList.indexOf(t);a>-1&&this.chooseList.splice(a,1)}this.chooseList.forEach((function(t){console.log("Checked values:",t)}))},getLockopen:function(t){var e=this;this.$confirm({title:"提示",content:"确认要解锁该持仓单?",onOk:function(){var a={state:0,positionId:t};(0,s.jP)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},handleTreadCancelAll:function(){this.ConfirmTreadDialogAll=!1;var t=this.$refs.createModal.form;t.resetFields()},handleTreadCancel:function(){this.ConfirmTreadDialog=!1;var t=this.$refs.createModal.form;t.resetFields()},handleCancel:function(){this.Lockvisibledialog=!1;var t=this.$refs.createModal.form;t.resetFields()},getTradeDialogokAll:function(){var t=this,e=this,a="",i="",n=[],o=[];this.$refs.createModal.form;this.isAll?(a=e.datalist.map((function(t){return t.id})).join(","),i=e.datalist.map((function(t){return t.agentId})).join(",")):(e.datalist.forEach((function(t){e.chooseList.includes(t.id)&&(n.push(t.id),o.push(t.agentId))})),a=n.join(","),i=o.join(","));var r=e.buyRatio,c=new URLSearchParams({ids:a,status:1,agentIds:i,buyRatio:r}).toString();console.log(c),(0,s.KD)(c).then((function(e){0==e.status?(t.ConfirmTreadDialogAll=!1,t.$message.success({content:e.data,duration:2}),t.getlist()):(t.$message.success({content:e.data,duration:2}),t.ConfirmTreadDialogdingAll=!1),t.ConfirmTreadDialogdingAll=!1})).catch((function(e){t.$message.error({content:"购买成交失败",duration:2}),t.ConfirmTreadDialogdingAll=!1}))},getTradeDialogok:function(){var t=this,e=this,a=this.$refs.createModal.form;a.validateFields((function(a,i){a||(t.oneTreadData.buyRatio=t.buyRatio,(0,s.xh)(t.oneTreadData).then((function(a){0==a.status?(t.ConfirmTreadDialog=!1,e.$message.success({content:a.data,duration:2}),e.getlist()):e.$message.success({content:a.data,duration:2})})).catch((function(a){e.$message.success({content:"购买成交失败",duration:2}),t.ConfirmTreadDialogding=!1})))}))},getDialogok:function(){var t=this,e=this.$refs.createModal.form;e.validateFields((function(a,i){if(!a){t.Lockvisibleloading=!0;var n={state:1,lockMsg:i.lockMsg,positionId:t.clickpositionId};(0,s.jP)(n).then((function(a){0==a.status?(t.Lockvisibledialog=!1,t.$message.success({content:a.msg,duration:2}),e.resetFields(),t.getlist()):t.$message.error({content:a.msg}),t.Lockvisibleloading=!1})).catch((function(t){reject(t)}))}}))},getinit:function(t){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:t},this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:this.queryParam.state}},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,c.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,(0,s.O3)(this.queryParam).then((function(e){t.datalist=[],e.data.list.forEach((function(e){1==e.backStatus&&0==e.buyNum||t.datalist.push(e)})),t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},m=u,g=a(6367),p=(0,g.A)(m,o,r,!1,null,"c2e30f2a",null),f=p.exports,h=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",{attrs:{label:"卖出时间"}},[e("a-range-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss"},on:{change:t.onChangeRangeDate},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"stockName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.stockName))]),3!=i.positionType?e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==i.stockPlate?"科创":i.stockPlate?i.stockPlate:"股票")+" ")]):e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" 大宗 ")]),e("p",[t._v("("+t._s(i.stockCode)+")")])],1)]],2)}},{key:"agentName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.agentName))]),e("p",[t._v("("+t._s(i.agentCode)+")")])])]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getFlatdetails(i)}},slot:"action"},[t._v("查看详情")]),e("a-divider",{attrs:{type:"vertical"}}),e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getDelflat(i.id)}},slot:"action"},[t._v("删除")])]}}])}),e("a-modal",{attrs:{title:"融资详情",width:1e3,visible:t.finacingDialog,footer:!1},on:{cancel:function(e){t.finacingDialog=!1}}},[e("a-descriptions",{attrs:{bordered:"",title:"".concat(t.clickitem.stockName,"(").concat(t.clickitem.stockCode,")")}},[e("a-descriptions-item",{attrs:{label:"用户名称（ID）"}},[e("span",[t._v(t._s(t.clickitem.nickName)+"（"+t._s(t.clickitem.userId)+"）")])]),e("a-descriptions-item",{attrs:{label:"股票类型"}},[e("a-tag",{attrs:{color:"科创"==t.clickitem.stockPlate?"blue":t.clickitem.stockPlate?"创业"==t.clickitem.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==t.clickitem.stockPlate?"科创":t.clickitem.stockPlate?t.clickitem.stockPlate:"A股")+" ")])],1),e("a-descriptions-item",{attrs:{label:"账户类型"}},[e("a-tag",{attrs:{color:1==t.clickitem.positionType?"blue":"green"}},[t._v(" "+t._s(1==t.clickitem.positionType?"模拟持仓":"正式持仓")+" ")])],1),e("a-descriptions-item",{attrs:{label:"持仓ID"}},[e("span",[t._v(" "+t._s(t.clickitem.id)+" ")])]),e("a-descriptions-item",{attrs:{label:"浮动盈亏"}},[e("span",{class:t.clickitem.profitAndLose>0?"reds":t.clickitem.profitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.profitAndLose)+" ")])]),e("a-descriptions-item",{attrs:{label:"总盈亏"}},[e("span",{class:t.clickitem.allProfitAndLose>0?"reds":t.clickitem.allProfitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.allProfitAndLose)+" ")])]),t.clickitem.now_price?e("a-descriptions-item",{attrs:{label:"当前价格"}},[e("span",{class:t.clickitem.now_price-t.clickitem.buyOrderPrice>0?"reds":t.clickitem.now_price-t.clickitem.buyOrderPrice<0?"greens":""},[t._v(" "+t._s(t.clickitem.now_price)+" ")])]):t._e(),e("a-descriptions-item",{attrs:{label:"卖出价格"}},[e("span",[t._v(" "+t._s(t.clickitem.sellOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入价格"}},[e("span",[t._v(" "+t._s(t.clickitem.buyOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入数量"}},[e("span",[t._v(" "+t._s(t.clickitem.buyNum)+" ")])]),e("a-descriptions-item",{attrs:{label:"买卖方向"}},[e("a-tag",{attrs:{color:"买涨"==t.clickitem.orderDirection?"red":"green"}},[t._v(" "+t._s(t.clickitem.orderDirection)+" ")])],1),e("a-descriptions-item",{attrs:{label:"杠杆倍数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderLever)+" ")])]),e("a-descriptions-item",{attrs:{label:"总市值"}},[e("span",[t._v(" "+t._s(t.clickitem.buyPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓天数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayDays)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"手续费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"印花税"}},[e("span",[t._v(" "+t._s(t.clickitem.orderSpread)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.buyOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"卖出时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.sellOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"买入订单号"}},[t._v(" "+t._s(t.clickitem.buyOrderId)+" ")]),e("a-descriptions-item",{attrs:{label:"卖出订单号"}},[t._v(" "+t._s(t.clickitem.sellOrderId)+" ")]),e("a-descriptions-item",{attrs:{label:"持仓订单号"}},[t._v(" "+t._s(t.clickitem.positionSn)+" ")]),e("a-descriptions-item",{attrs:{label:"锁仓原因"}},[t._v(" "+t._s(t.clickitem.lockMsg)+" ")])],1)],1)],1)},v=[],y={name:"financingflat",data:function(){var t=this;return{columns:[{title:"融资名称",dataIndex:"stockName",align:"center",width:180,scopedSlots:{customRender:"stockName"}},{title:"上级代理",dataIndex:"agentName",align:"center",width:180,scopedSlots:{customRender:"agentName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"nickName",align:"center",customRender:function(t,e,a){return"".concat(e.nickName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入价",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"卖出价",dataIndex:"sellOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（股）",dataIndex:"buyNum",align:"center"},{title:"总市值",dataIndex:"buyPrice",align:"center"},{title:"杠杆倍数",dataIndex:"orderLever",align:"center"},{title:"手续费",dataIndex:"orderFee",align:"center"},{title:"印花税",dataIndex:"orderSpread",align:"center"},{title:"留仓费",dataIndex:"orderStayFee",align:"center"},{title:"留仓天数",dataIndex:"orderStayDays",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"卖出时间",dataIndex:"sellOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:2,beginTime:"",endTime:""},datalist:[],agentlist:[],agentloading:!1,times:[],finacingDialog:!1,clickitem:{},agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getFlatdetails:function(t){this.clickitem=t,this.finacingDialog=!0},getDelflat:function(t){var e=this;this.$confirm({title:"提示",content:"确认删除平仓单吗？此操作不可恢复",onOk:function(){var a={positionId:t};(0,s.Zb)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:2,beginTime:"",endTime:""},this.times=[],this.getlist()},onChangeRangeDate:function(t,e){this.queryParam.beginTime=e[0],this.queryParam.endTime=e[1]},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:2,beginTime:"",endTime:""},this.times=[]},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,c.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this,e=this;this.loading=!0,(0,s.O3)(this.queryParam).then((function(a){t.datalist=a.data.list,t.pagination.total=a.data.total,setTimeout((function(){e.loading=!1}),500)}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},k=y,_=(0,g.A)(k,h,v,!1,null,"3dce930c",null),b=_.exports,P=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",{attrs:{label:"卖出时间"}},[e("a-range-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss"},on:{change:t.onChangeRangeDate},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"stockName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.stockName))]),3!=i.positionType?e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==i.stockPlate?"科创":i.stockPlate?i.stockPlate:"股票")+" ")]):e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" 大宗 ")]),e("p",[t._v("("+t._s(i.stockCode)+")")])],1)]],2)}},{key:"agentName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.agentName))]),e("p",[t._v("("+t._s(i.agentCode)+")")])])]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getDelflat(i.id)}},slot:"action"},[t._v("删除")])]}}])}),e("a-modal",{attrs:{title:"融资详情",width:1e3,visible:t.finacingDialog,footer:!1},on:{cancel:function(e){t.finacingDialog=!1}}},[e("a-descriptions",{attrs:{bordered:"",title:"".concat(t.clickitem.stockName,"(").concat(t.clickitem.stockCode,")")}},[e("a-descriptions-item",{attrs:{label:"用户名称（ID）"}},[e("span",[t._v(t._s(t.clickitem.nickName)+"（"+t._s(t.clickitem.userId)+"）")])]),e("a-descriptions-item",{attrs:{label:"股票类型"}},[e("a-tag",{attrs:{color:"科创"==t.clickitem.stockPlate?"blue":t.clickitem.stockPlate?"创业"==t.clickitem.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==t.clickitem.stockPlate?"科创":t.clickitem.stockPlate?t.clickitem.stockPlate:"A股")+" ")])],1),e("a-descriptions-item",{attrs:{label:"账户类型"}},[e("a-tag",{attrs:{color:1==t.clickitem.positionType?"blue":"green"}},[t._v(" "+t._s(1==t.clickitem.positionType?"模拟持仓":"正式持仓")+" ")])],1),e("a-descriptions-item",{attrs:{label:"持仓ID"}},[e("span",[t._v(" "+t._s(t.clickitem.id)+" ")])]),e("a-descriptions-item",{attrs:{label:"浮动盈亏"}},[e("span",{class:t.clickitem.profitAndLose>0?"reds":t.clickitem.profitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.profitAndLose)+" ")])]),e("a-descriptions-item",{attrs:{label:"总盈亏"}},[e("span",{class:t.clickitem.allProfitAndLose>0?"reds":t.clickitem.allProfitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.allProfitAndLose)+" ")])]),t.clickitem.now_price?e("a-descriptions-item",{attrs:{label:"当前价格"}},[e("span",{class:t.clickitem.now_price-t.clickitem.buyOrderPrice>0?"reds":t.clickitem.now_price-t.clickitem.buyOrderPrice<0?"greens":""},[t._v(" "+t._s(t.clickitem.now_price)+" ")])]):t._e(),e("a-descriptions-item",{attrs:{label:"卖出价格"}},[e("span",[t._v(" "+t._s(t.clickitem.sellOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入价格"}},[e("span",[t._v(" "+t._s(t.clickitem.buyOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入数量"}},[e("span",[t._v(" "+t._s(t.clickitem.orderNum)+" ")])]),e("a-descriptions-item",{attrs:{label:"买卖方向"}},[e("a-tag",{attrs:{color:"买涨"==t.clickitem.orderDirection?"red":"green"}},[t._v(" "+t._s(t.clickitem.orderDirection)+" ")])],1),e("a-descriptions-item",{attrs:{label:"杠杆倍数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderLever)+" ")])]),e("a-descriptions-item",{attrs:{label:"总市值"}},[e("span",[t._v(" "+t._s(t.clickitem.orderTotalPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓天数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayDays)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"手续费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"印花税"}},[e("span",[t._v(" "+t._s(t.clickitem.orderSpread)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.buyOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"卖出时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.sellOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"买入订单号"}},[t._v(" "+t._s(t.clickitem.buyOrderId)+" ")]),e("a-descriptions-item",{attrs:{label:"卖出订单号"}},[t._v(" "+t._s(t.clickitem.sellOrderId)+" ")]),e("a-descriptions-item",{attrs:{label:"持仓订单号"}},[t._v(" "+t._s(t.clickitem.positionSn)+" ")]),e("a-descriptions-item",{attrs:{label:"锁仓原因"}},[t._v(" "+t._s(t.clickitem.lockMsg)+" ")])],1)],1)],1)},I=[],S={name:"financingflat",data:function(){var t=this;return{columns:[{title:"融资名称",dataIndex:"stockName",align:"center",width:180,scopedSlots:{customRender:"stockName"}},{title:"上级代理",dataIndex:"agentName",align:"center",width:180,scopedSlots:{customRender:"agentName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"nickName",align:"center",customRender:function(t,e,a){return"".concat(e.nickName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入价",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"卖出价",dataIndex:"sellOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（股）",dataIndex:"orderNum",align:"center"},{title:"总市值",dataIndex:"orderTotalPrice",align:"center"},{title:"杠杆倍数",dataIndex:"orderLever",align:"center"},{title:"手续费",dataIndex:"orderFee",align:"center"},{title:"印花税",dataIndex:"orderSpread",align:"center"},{title:"留仓费",dataIndex:"orderStayFee",align:"center"},{title:"留仓天数",dataIndex:"orderStayDays",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"卖出时间",dataIndex:"sellOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:3,beginTime:"",endTime:""},datalist:[],agentlist:[],agentloading:!1,times:[],finacingDialog:!1,clickitem:{},agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getFlatdetails:function(t){this.clickitem=t,this.finacingDialog=!0},getDelflat:function(t){var e=this;this.$confirm({title:"提示",content:"确认删除平仓单吗？此操作不可恢复",onOk:function(){var a={positionId:t};(0,s.Zb)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:3,beginTime:"",endTime:""},this.times=[],this.getlist()},onChangeRangeDate:function(t,e){this.queryParam.beginTime=e[0],this.queryParam.endTime=e[1]},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:3,beginTime:"",endTime:""},this.times=[]},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,c.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this,e=this;this.loading=!0,(0,s.O3)(this.queryParam).then((function(a){t.datalist=a.data.list,t.pagination.total=a.data.total,setTimeout((function(){e.loading=!1}),500)}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},x=S,T=(0,g.A)(x,P,I,!1,null,"553450ba",null),C=T.exports,q=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"indexName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.indexName))]),e("p",[t._v("("+t._s(i.indexCode)+")")])])]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"now_price",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:Number(i.now_price)-i.buyOrderPrice<0?"greens":Number(i.now_price)-i.buyOrderPrice>0?"reds":""},[t._v(" "+t._s(Number(i.now_price).toFixed(2))+" ")])])]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[0==i.isLock?e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){t.Lockvisibledialig=!0,t.clickpositionId=i.id}},slot:"action"},[t._v("锁仓")]):e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getLockopen(i.id)}},slot:"action"},[t._v("解锁")]),e("a-divider",{attrs:{type:"vertical"}}),e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getCompulsoryclosing(i.positionSn)}},slot:"action"},[t._v("强制平仓")])]}}])}),e("a-modal",{attrs:{title:"锁仓",width:640,visible:t.Lockvisibledialig,confirmLoading:t.Lockvisibleloading},on:{ok:t.getDialogok,cancel:t.handleCancel}},[e("a-form",{ref:"createModal",attrs:{form:t.Lockvisibleform}},[e("a-form-item",[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["lockMsg",{rules:[{required:!0,message:"请输入锁仓原因！",whitespace:!0}]}],expression:"['lockMsg', { rules: [{ required: true, message: '请输入锁仓原因！', whitespace: true }] }]"}],attrs:{placeholder:"请输入锁仓原因！"}})],1)],1)],1)],1)},D=[],N={name:"index-hold",data:function(){var t=this;return{columns:[{title:"指数名称",dataIndex:"indexName",align:"center",width:180,scopedSlots:{customRender:"indexName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"realName",align:"center",customRender:function(t,e,a){return"".concat(e.realName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入点数",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"最新点数",dataIndex:"now_price",align:"center",scopedSlots:{customRender:"now_price"}},{title:"保证金",dataIndex:"allDepositAmt",align:"center"},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（手）",dataIndex:"orderNum",align:"center"},{title:"点浮动价",dataIndex:"eachPoint",align:"center"},{title:"双边手续费",dataIndex:"orderFee",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0},datalist:[],agentlist:[],agentloading:!1,Lockvisibledialig:!1,Lockvisibleloading:!1,Lockvisibleform:this.$form.createForm(this),clickpositionId:"",agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getCompulsoryclosing:function(t){var e=this;this.$confirm({title:"提示",content:"确认要强制平仓吗?",onOk:function(){var a={positionSn:t};(0,s._m)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:"平仓失败"})})).catch((function(t){e.$message.error({content:"平仓失败"})}))},onCancel:function(){console.log("Cancel")}})},getLockopen:function(t){var e=this;this.$confirm({title:"提示",content:"确认要解锁该持仓单?",onOk:function(){var a={state:0,positionId:t};(0,s.dN)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},handleCancel:function(){this.Lockvisibledialig=!1;var t=this.$refs.createModal.form;t.resetFields()},getDialogok:function(){var t=this,e=this.$refs.createModal.form;e.validateFields((function(a,i){if(!a){t.Lockvisibleloading=!0;var n={state:1,lockMsg:i.lockMsg,positionId:t.clickpositionId};(0,s.dN)(n).then((function(a){0==a.status?(t.Lockvisibledialig=!1,t.$message.success({content:a.msg,duration:2}),e.resetFields(),t.getlist()):t.$message.error({content:a.msg}),t.Lockvisibleloading=!1}))}}))},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0},this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0}},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,c.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,(0,s.iK)(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},w=N,L=(0,g.A)(w,q,D,!1,null,"258bb396",null),A=L.exports,R=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",{attrs:{label:"卖出时间"}},[e("a-range-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss"},on:{change:t.onChangeRangeDate},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"indexName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.indexName))]),e("p",[t._v("("+t._s(i.indexCode)+")")])])]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"now_price",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:Number(i.now_price)-i.buyOrderPrice<0?"greens":Number(i.now_price)-i.buyOrderPrice>0?"reds":""},[t._v(" "+t._s(Number(i.now_price).toFixed(2))+" ")])])]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getIndexdetails(i)}},slot:"action"},[t._v("查看详情")]),e("a-divider",{attrs:{type:"vertical"}}),e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getDelflat(i.id)}},slot:"action"},[t._v("删除")])]}}])}),e("a-modal",{attrs:{title:"指数详情",width:1e3,visible:t.indexDialog,footer:!1},on:{cancel:function(e){t.indexDialog=!1}}},[e("a-descriptions",{attrs:{bordered:"",title:"".concat(t.clickitem.indexName,"(").concat(t.clickitem.indexCode,")")}},[e("a-descriptions-item",{attrs:{label:"用户名称（ID）"}},[e("span",[t._v(t._s(t.clickitem.realName)+"（"+t._s(t.clickitem.userId)+"）")])]),e("a-descriptions-item",{attrs:{label:"账户类型"}},[e("a-tag",{attrs:{color:1==t.clickitem.positionType?"blue":"green"}},[t._v(" "+t._s(1==t.clickitem.positionType?"模拟持仓":"正式持仓")+" ")])],1),e("a-descriptions-item",{attrs:{label:"持仓ID"}},[e("span",[t._v(" "+t._s(t.clickitem.id)+" ")])]),e("a-descriptions-item",{attrs:{label:"浮动盈亏"}},[e("span",{class:t.clickitem.profitAndLose>0?"reds":t.clickitem.profitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.profitAndLose)+" ")])]),e("a-descriptions-item",{attrs:{label:"总盈亏"}},[e("span",{class:t.clickitem.allProfitAndLose>0?"reds":t.clickitem.allProfitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.allProfitAndLose)+" ")])]),t.clickitem.now_price?e("a-descriptions-item",{attrs:{label:"当前价格"}},[e("span",{class:t.clickitem.now_price-t.clickitem.buyOrderPrice>0?"reds":t.clickitem.now_price-t.clickitem.buyOrderPrice<0?"greens":""},[t._v(" "+t._s(t.clickitem.now_price)+" ")])]):t._e(),e("a-descriptions-item",{attrs:{label:"卖出点数"}},[e("span",[t._v(" "+t._s(t.clickitem.sellOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入点数"}},[e("span",[t._v(" "+t._s(t.clickitem.buyOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入数量（手）"}},[e("span",[t._v(" "+t._s(t.clickitem.orderNum)+" ")])]),e("a-descriptions-item",{attrs:{label:"买卖方向"}},[e("a-tag",{attrs:{color:"买涨"==t.clickitem.orderDirection?"red":"green"}},[t._v(" "+t._s(t.clickitem.orderDirection)+" ")])],1),e("a-descriptions-item",{attrs:{label:"保证金"}},[e("span",[t._v(" "+t._s(t.clickitem.allDepositAmt)+" ")])]),e("a-descriptions-item",{attrs:{label:"点浮动价"}},[e("span",[t._v(" "+t._s(t.clickitem.eachPoint)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓天数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayDays)+" ")])]),e("a-descriptions-item",{attrs:{label:"手续费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.buyOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"卖出时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.sellOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"持仓订单号"}},[t._v(" "+t._s(t.clickitem.positionSn)+" ")]),e("a-descriptions-item",{attrs:{label:"锁仓原因"}},[t._v(" "+t._s(t.clickitem.lockMsg)+" ")])],1)],1)],1)},$=[],O={name:"index-flat",data:function(){var t=this;return{columns:[{title:"指数名称",dataIndex:"indexName",align:"center",width:180,scopedSlots:{customRender:"indexName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"realName",align:"center",customRender:function(t,e,a){return"".concat(e.realName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入点数",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"卖出点数",dataIndex:"sellOrderPrice",align:"center",scopedSlots:{customRender:"sellOrderPrice"}},{title:"保证金",dataIndex:"allDepositAmt",align:"center"},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（手）",dataIndex:"orderNum",align:"center"},{title:"点浮动价",dataIndex:"eachPoint",align:"center"},{title:"手续费",dataIndex:"orderFee",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"卖出时间",dataIndex:"sellOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},datalist:[],agentlist:[],agentloading:!1,times:[],indexDialog:!1,clickitem:{},agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getIndexdetails:function(t){this.clickitem=t,this.indexDialog=!0},getDelflat:function(t){var e=this;this.$confirm({title:"提示",content:"确认删除平仓单吗？此操作不可恢复",onOk:function(){var a={positionId:t};(0,s.NW)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},this.times=[],this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1},this.times=[]},onChangeRangeDate:function(t,e){this.queryParam.beginTime=e[0],this.queryParam.endTime=e[1]},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,c.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,(0,s.iK)(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},z=O,F=(0,g.A)(z,R,$,!1,null,"6a8f2590",null),Y=F.exports,M=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"nowPrice",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:Number(i.nowPrice)-i.buyOrderPrice<0?"greens":Number(i.nowPrice)-i.buyOrderPrice>0?"reds":""},[t._v(" "+t._s(Number(i.nowPrice).toFixed(2))+" ")])])]],2)}},{key:"profitAndLose",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+t._s(i.coinCode)+" ")]),e("P",[t._v("≈"+t._s((i.profitAndLose*i.coinRate).toFixed(2))+" CNY")])],1)]],2)}},{key:"allProfitAndLose",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+t._s(i.coinCode)+" ")]),e("P",[t._v("≈"+t._s((i.allProfitAndLose*i.coinRate).toFixed(2))+" CNY")])],1)]],2)}},{key:"action",fn:function(a,i){return[0==i.isLock?e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){t.Lockvisibledialig=!0,t.clickpositionId=i.id}},slot:"action"},[t._v("锁仓")]):e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getLockopen(i.id)}},slot:"action"},[t._v("解锁")]),e("a-divider",{attrs:{type:"vertical"}}),e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getCompulsoryclosing(i.positionSn)}},slot:"action"},[t._v("强制平仓")])]}}])}),e("a-modal",{attrs:{title:"锁仓",width:640,visible:t.Lockvisibledialig,confirmLoading:t.Lockvisibleloading},on:{ok:t.getDialogok,cancel:t.handleCancel}},[e("a-form",{ref:"createModal",attrs:{form:t.Lockvisibleform}},[e("a-form-item",[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["lockMsg",{rules:[{required:!0,message:"请输入锁仓原因！",whitespace:!0}]}],expression:"['lockMsg', { rules: [{ required: true, message: '请输入锁仓原因！', whitespace: true }] }]"}],attrs:{placeholder:"请输入锁仓原因！"}})],1)],1)],1)],1)},H=[],j={name:"futures-hold",data:function(){var t=this;return{columns:[{title:"期货名称",dataIndex:"futuresName",align:"center",width:180,customRender:function(t,e,a){return"".concat(e.futuresName,"（").concat(e.futuresCode,"）")}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"realName",align:"center",customRender:function(t,e,a){return"".concat(e.realName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入点数",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"最新点数",dataIndex:"nowPrice",align:"center",scopedSlots:{customRender:"nowPrice"}},{title:"保证金（CNY）",dataIndex:"allDepositAmt",align:"center"},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（手）",dataIndex:"orderNum",align:"center"},{title:"每标准手",dataIndex:"futuresStandard",align:"center",customRender:function(t,e,a){return e.futuresStandard+e.futuresUnit}},{title:"手续费",dataIndex:"orderFee",align:"center",customRender:function(t,e,a){return"".concat(e.orderFee," ").concat(e.coinCode," ≈ ").concat((e.orderFee*e.coinRate).toFixed(2)," CNY")}},{title:"当前汇率",dataIndex:"coinCode",align:"center",customRender:function(t,e,a){return"1".concat(e.coinCode," ≈ ").concat(e.coinRate,"CNY")}},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0},datalist:[],agentlist:[],agentloading:!1,Lockvisibledialig:!1,Lockvisibleloading:!1,Lockvisibleform:this.$form.createForm(this),clickpositionId:"",agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getCompulsoryclosing:function(t){var e=this;this.$confirm({title:"提示",content:"确认要强制平仓吗?",onOk:function(){var a={positionSn:t};(0,s.Ks)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:"平仓失败"})}))},onCancel:function(){console.log("Cancel")}})},getLockopen:function(t){var e=this;this.$confirm({title:"提示",content:"确认要解锁该持仓单?",onOk:function(){var a={state:0,positionId:t};(0,s.dX)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},handleCancel:function(){this.Lockvisibledialig=!1;var t=this.$refs.createModal.form;t.resetFields()},getDialogok:function(){var t=this,e=this.$refs.createModal.form;e.validateFields((function(a,i){if(!a){t.Lockvisibleloading=!0;var n={state:1,lockMsg:i.lockMsg,positionId:t.clickpositionId};(0,s.dX)(n).then((function(a){0==a.status?(t.Lockvisibledialig=!1,t.$message.success({content:a.msg,duration:2}),e.resetFields(),t.getlist()):t.$message.error({content:a.msg}),t.Lockvisibleloading=!1}))}}))},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0},this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0}},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,c.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,(0,s.AJ)(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},K=j,B=(0,g.A)(K,M,H,!1,null,"2543b533",null),E=B.exports,U=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",{attrs:{label:"卖出时间"}},[e("a-range-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss"},on:{change:t.onChangeRangeDate},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"profitAndLose",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+t._s(i.coinCode)+" ")]),e("P",[t._v("≈"+t._s((i.profitAndLose*i.coinRate).toFixed(2))+" CNY")])],1)]],2)}},{key:"allProfitAndLose",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+t._s(i.coinCode)+" ")]),e("P",[t._v("≈"+t._s((i.allProfitAndLose*i.coinRate).toFixed(2))+" CNY")])],1)]],2)}},{key:"action",fn:function(a,i){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getFuturesdetails(i)}},slot:"action"},[t._v("查看详情")]),e("a-divider",{attrs:{type:"vertical"}}),e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getDelflat(i.id)}},slot:"action"},[t._v("删除")])]}}])}),e("a-modal",{attrs:{title:"期货详情",width:1200,visible:t.futuresDialog,footer:!1},on:{cancel:function(e){t.futuresDialog=!1}}},[e("a-descriptions",{attrs:{bordered:"",title:"".concat(t.clickitem.futuresName,"(").concat(t.clickitem.futuresCode,")")}},[e("a-descriptions-item",{attrs:{label:"用户名称（ID）"}},[e("span",[t._v(t._s(t.clickitem.realName)+"（"+t._s(t.clickitem.userId)+"）")])]),e("a-descriptions-item",{attrs:{label:"账户类型"}},[e("a-tag",{attrs:{color:1==t.clickitem.positionType?"blue":"green"}},[t._v(" "+t._s(1==t.clickitem.positionType?"模拟持仓":"正式持仓")+" ")])],1),e("a-descriptions-item",{attrs:{label:"持仓ID"}},[e("span",[t._v(" "+t._s(t.clickitem.id)+" ")])]),e("a-descriptions-item",{attrs:{label:"浮动盈亏"}},[e("span",{class:t.clickitem.profitAndLose>0?"reds":t.clickitem.profitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.profitAndLose)+t._s(t.clickitem.coinCode)+" ≈ "+t._s((t.clickitem.profitAndLose*t.clickitem.coinRate).toFixed(2))+" CNY ")])]),e("a-descriptions-item",{attrs:{label:"总盈亏"}},[e("span",{class:t.clickitem.allProfitAndLose>0?"reds":t.clickitem.allProfitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.allProfitAndLose)+t._s(t.clickitem.coinCode)+" ≈ "+t._s((t.clickitem.allProfitAndLose*t.clickitem.coinRate).toFixed(2))+" CNY ")])]),t.clickitem.now_price?e("a-descriptions-item",{attrs:{label:"当前价格"}},[e("span",{class:t.clickitem.now_price-t.clickitem.buyOrderPrice>0?"reds":t.clickitem.now_price-t.clickitem.buyOrderPrice<0?"greens":""},[t._v(" "+t._s(t.clickitem.now_price)+" ")])]):t._e(),e("a-descriptions-item",{attrs:{label:"卖出点数"}},[e("span",[t._v(" "+t._s(t.clickitem.sellOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入点数"}},[e("span",[t._v(" "+t._s(t.clickitem.buyOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入数量（手）"}},[e("span",[t._v(" "+t._s(t.clickitem.orderNum)+" ")])]),e("a-descriptions-item",{attrs:{label:"每标准手"}},[e("span",[t._v(" "+t._s(t.clickitem.futuresStandard+t.clickitem.futuresUnit)+" ")])]),e("a-descriptions-item",{attrs:{label:"买卖方向"}},[e("a-tag",{attrs:{color:"买涨"==t.clickitem.orderDirection?"red":"green"}},[t._v(" "+t._s(t.clickitem.orderDirection)+" ")])],1),e("a-descriptions-item",{attrs:{label:"保证金(CNY)"}},[e("span",[t._v(" "+t._s(t.clickitem.allDepositAmt)+" ")])]),e("a-descriptions-item",{attrs:{label:"手续费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderFee)+t._s(t.clickitem.coinCode)+" ≈ "+t._s((t.clickitem.orderFee*t.clickitem.coinRate).toFixed(2))+" CNY ")])]),e("a-descriptions-item",{attrs:{label:"当前汇率"}},[e("span",[t._v(" 1 "+t._s(t.clickitem.coinCode)+" ≈ "+t._s(t.clickitem.coinRate)+" CNY ")])]),e("a-descriptions-item",{attrs:{label:"买入时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.buyOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"卖出时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.sellOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"持仓订单号"}},[t._v(" "+t._s(t.clickitem.positionSn)+" ")]),e("a-descriptions-item",{attrs:{label:"锁仓原因"}},[t._v(" "+t._s(t.clickitem.lockMsg)+" ")])],1)],1)],1)},J=[],X={name:"setting",data:function(){var t=this;return{columns:[{title:"期货名称",dataIndex:"futuresName",align:"center",width:180,customRender:function(t,e,a){return"".concat(e.futuresName,"（").concat(e.futuresCode,"）")}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"realName",align:"center",customRender:function(t,e,a){return"".concat(e.realName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入点数",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"卖出点数",dataIndex:"sellOrderPrice",align:"center"},{title:"保证金（CNY）",dataIndex:"allDepositAmt",align:"center"},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（手）",dataIndex:"orderNum",align:"center"},{title:"每标准手",dataIndex:"futuresStandard",align:"center",customRender:function(t,e,a){return e.futuresStandard+e.futuresUnit}},{title:"手续费",dataIndex:"orderFee",align:"center",customRender:function(t,e,a){return"".concat(e.orderFee," ").concat(e.coinCode," ≈ ").concat((e.orderFee*e.coinRate).toFixed(2)," CNY")}},{title:"当前汇率",dataIndex:"coinCode",align:"center",customRender:function(t,e,a){return"1".concat(e.coinCode," ≈ ").concat(e.coinRate,"CNY")}},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"卖出时间",dataIndex:"sellOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?d()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},datalist:[],agentlist:[],agentloading:!1,times:[],futuresDialog:!1,clickitem:{},agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getFuturesdetails:function(t){this.clickitem=t,this.futuresDialog=!0},getDelflat:function(t){var e=this;this.$confirm({title:"提示",content:"确认删除平仓单吗？此操作不可恢复",onOk:function(){var a={positionId:t};(0,s.hA)(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},this.times=[],this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},this.times=[]},onChangeRangeDate:function(t,e){this.queryParam.beginTime=e[0],this.queryParam.endTime=e[1]},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,c.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,(0,s.AJ)(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},Z=X,W=(0,g.A)(Z,U,J,!1,null,"b2867612",null),G=W.exports,Q={name:"Financing",components:{financingHold:f,financingFlat:b,indexHold:A,indexFlat:Y,futuresHold:E,futuresFlat:G,financingFlatCancel:C},data:function(){return{}},methods:{gettabchange:function(t){console.log("val",t),0==t?this.$refs.financingHold.getinit(0):1==t?this.$refs.financingHold.getinit(1):2==t?this.$refs.financingFlats.getinit():3==t?this.$refs.financingCancel.getinit():4==t?this.$refs.indexFlat.getinit():5==t?this.$refs.futuresHold.getinit():6==t&&this.$refs.futuresFlat.getinit()}}},V=Q,tt=(0,g.A)(V,i,n,!1,null,"424ff0de",null),et=tt.exports}}]);