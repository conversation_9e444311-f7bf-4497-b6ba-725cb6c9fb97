(function(){var e={22:function(e,t,n){"use strict";n.r(t),t["default"]={"form.basic-form.basic.title":"基础表单","form.basic-form.basic.description":"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。","form.basic-form.title.label":"标题","form.basic-form.title.placeholder":"给目标起个名字","form.basic-form.title.required":"请输入标题","form.basic-form.date.label":"起止日期","form.basic-form.placeholder.start":"开始日期","form.basic-form.placeholder.end":"结束日期","form.basic-form.date.required":"请选择起止日期","form.basic-form.goal.label":"目标描述","form.basic-form.goal.placeholder":"请输入你的阶段性工作目标","form.basic-form.goal.required":"请输入目标描述","form.basic-form.standard.label":"衡量标准","form.basic-form.standard.placeholder":"请输入衡量标准","form.basic-form.standard.required":"请输入衡量标准","form.basic-form.client.label":"客户","form.basic-form.client.required":"请描述你服务的客户","form.basic-form.label.tooltip":"目标的服务对象","form.basic-form.client.placeholder":"请描述你服务的客户，内部客户直接 @姓名／工号","form.basic-form.invites.label":"邀评人","form.basic-form.invites.placeholder":"请直接 @姓名／工号，最多可邀请 5 人","form.basic-form.weight.label":"权重","form.basic-form.weight.placeholder":"请输入","form.basic-form.public.label":"目标公开","form.basic-form.label.help":"客户、邀评人默认被分享","form.basic-form.radio.public":"公开","form.basic-form.radio.partially-public":"部分公开","form.basic-form.radio.private":"不公开","form.basic-form.publicUsers.placeholder":"公开给","form.basic-form.option.A":"同事一","form.basic-form.option.B":"同事二","form.basic-form.option.C":"同事三","form.basic-form.email.required":"请输入邮箱地址！","form.basic-form.email.wrong-format":"邮箱地址格式错误！","form.basic-form.userName.required":"请输入用户名!","form.basic-form.password.required":"请输入密码！","form.basic-form.password.twice":"两次输入的密码不匹配!","form.basic-form.strength.msg":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","form.basic-form.strength.strong":"强度：强","form.basic-form.strength.medium":"强度：中","form.basic-form.strength.short":"强度：太短","form.basic-form.confirm-password.required":"请确认密码！","form.basic-form.phone-number.required":"请输入手机号！","form.basic-form.phone-number.wrong-format":"手机号格式错误！","form.basic-form.verification-code.required":"请输入验证码！","form.basic-form.form.get-captcha":"获取验证码","form.basic-form.captcha.second":"秒","form.basic-form.form.optional":"（选填）","form.basic-form.form.submit":"提交","form.basic-form.form.save":"保存","form.basic-form.email.placeholder":"邮箱","form.basic-form.password.placeholder":"至少6位密码，区分大小写","form.basic-form.confirm-password.placeholder":"确认密码","form.basic-form.phone-number.placeholder":"手机号","form.basic-form.verification-code.placeholder":"验证码"}},3586:function(e,t,n){"use strict";n.r(t),t["default"]={"account.settings.menuMap.basic":"基本设置","account.settings.menuMap.security":"安全设置","account.settings.menuMap.custom":"个性化","account.settings.menuMap.binding":"账号绑定","account.settings.menuMap.notification":"新消息通知","account.settings.basic.avatar":"头像","account.settings.basic.change-avatar":"更换头像","account.settings.basic.email":"邮箱","account.settings.basic.email-message":"请输入您的邮箱!","account.settings.basic.nickname":"昵称","account.settings.basic.nickname-message":"请输入您的昵称!","account.settings.basic.profile":"个人简介","account.settings.basic.profile-message":"请输入个人简介!","account.settings.basic.profile-placeholder":"个人简介","account.settings.basic.country":"国家/地区","account.settings.basic.country-message":"请输入您的国家或地区!","account.settings.basic.geographic":"所在省市","account.settings.basic.geographic-message":"请输入您的所在省市!","account.settings.basic.address":"街道地址","account.settings.basic.address-message":"请输入您的街道地址!","account.settings.basic.phone":"联系电话","account.settings.basic.phone-message":"请输入您的联系电话!","account.settings.basic.update":"更新基本信息","account.settings.basic.update.success":"更新基本信息成功","account.settings.security.strong":"强","account.settings.security.medium":"中","account.settings.security.weak":"弱","account.settings.security.password":"账户密码","account.settings.security.password-description":"当前密码强度：","account.settings.security.phone":"密保手机","account.settings.security.phone-description":"已绑定手机：","account.settings.security.question":"密保问题","account.settings.security.question-description":"未设置密保问题，密保问题可有效保护账户安全","account.settings.security.email":"备用邮箱","account.settings.security.email-description":"已绑定邮箱：","account.settings.security.mfa":"MFA 设备","account.settings.security.mfa-description":"未绑定 MFA 设备，绑定后，可以进行二次确认","account.settings.security.modify":"修改","account.settings.security.set":"设置","account.settings.security.bind":"绑定","account.settings.binding.taobao":"绑定淘宝","account.settings.binding.taobao-description":"当前未绑定淘宝账号","account.settings.binding.alipay":"绑定支付宝","account.settings.binding.alipay-description":"当前未绑定支付宝账号","account.settings.binding.dingding":"绑定钉钉","account.settings.binding.dingding-description":"当前未绑定钉钉账号","account.settings.binding.bind":"绑定","account.settings.notification.password":"账户密码","account.settings.notification.password-description":"其他用户的消息将以站内信的形式通知","account.settings.notification.messages":"系统消息","account.settings.notification.messages-description":"系统消息将以站内信的形式通知","account.settings.notification.todo":"待办任务","account.settings.notification.todo-description":"待办任务将以站内信的形式通知","account.settings.settings.open":"开","account.settings.settings.close":"关"}},3837:function(){},5839:function(e,t,n){var a={"./en-US":[13978],"./en-US.js":[13978],"./en-US/account":[27678,1980],"./en-US/account.js":[27678,1980],"./en-US/account/settings":[17200,2098],"./en-US/account/settings.js":[17200,2098],"./en-US/dashboard":[28813,6345],"./en-US/dashboard.js":[28813,6345],"./en-US/dashboard/analysis":[43786,8376],"./en-US/dashboard/analysis.js":[43786,8376],"./en-US/form":[57355,7533],"./en-US/form.js":[57355,7533],"./en-US/form/basicForm":[29072,8438],"./en-US/form/basicForm.js":[29072,8438],"./en-US/global":[54598,2418],"./en-US/global.js":[54598,2418],"./en-US/menu":[52222,7254],"./en-US/menu.js":[52222,7254],"./en-US/result":[29624,5924],"./en-US/result.js":[29624,5924],"./en-US/result/fail":[30231,77],"./en-US/result/fail.js":[30231,77],"./en-US/result/success":[48900,5802],"./en-US/result/success.js":[48900,5802],"./en-US/setting":[69793,4729],"./en-US/setting.js":[69793,4729],"./en-US/user":[63934,4606],"./en-US/user.js":[63934,4606],"./zh-CN":[92768,7644],"./zh-CN.js":[92768,7644],"./zh-CN/account":[34720],"./zh-CN/account.js":[34720],"./zh-CN/account/settings":[3586],"./zh-CN/account/settings.js":[3586],"./zh-CN/dashboard":[89183],"./zh-CN/dashboard.js":[89183],"./zh-CN/dashboard/analysis":[15344],"./zh-CN/dashboard/analysis.js":[15344],"./zh-CN/form":[69157],"./zh-CN/form.js":[69157],"./zh-CN/form/basicForm":[22],"./zh-CN/form/basicForm.js":[22],"./zh-CN/global":[78152],"./zh-CN/global.js":[78152],"./zh-CN/menu":[48152],"./zh-CN/menu.js":[48152],"./zh-CN/result":[56086],"./zh-CN/result.js":[56086],"./zh-CN/result/fail":[69161],"./zh-CN/result/fail.js":[69161],"./zh-CN/result/success":[19994],"./zh-CN/result/success.js":[19994],"./zh-CN/setting":[34935],"./zh-CN/setting.js":[34935],"./zh-CN/user":[98924],"./zh-CN/user.js":[98924]};function i(e){if(!n.o(a,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=a[e],i=t[0];return Promise.all(t.slice(1).map(n.e)).then((function(){return n(i)}))}i.keys=function(){return Object.keys(a)},i.id=5839,e.exports=i},13978:function(e,t,n){"use strict";n.r(t);var a=n(82083),i=n(14823),s=n(75202),r=n.n(s),o=n(78152),c=n(48152),d=n(34935),u=n(98924),l=n(89183),m=n(69157),f=n(56086),p=n(34720),h={antLocale:i.A,momentName:"zh-cn",momentLocale:r()};t["default"]=(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({message:"-","layouts.usermenu.dialog.title":"信息","layouts.usermenu.dialog.content":"您确定要注销吗？","layouts.userLayout.title":"Stock Admin 是西湖区最具影响力的 Web 设计规范"},h),o["default"]),c["default"]),d["default"]),u["default"]),l["default"]),m["default"]),f["default"]),p["default"])},15344:function(e,t,n){"use strict";n.r(t),t["default"]={"dashboard.analysis.test":"工专路 {no} 号店","dashboard.analysis.introduce":"指标说明","dashboard.analysis.total-sales":"总销售额","dashboard.analysis.day-sales":"日均销售额￥","dashboard.analysis.visits":"访问量","dashboard.analysis.visits-trend":"访问量趋势","dashboard.analysis.visits-ranking":"门店访问量排名","dashboard.analysis.day-visits":"日访问量","dashboard.analysis.week":"周同比","dashboard.analysis.day":"日同比","dashboard.analysis.payments":"支付笔数","dashboard.analysis.conversion-rate":"转化率","dashboard.analysis.operational-effect":"运营活动效果","dashboard.analysis.sales-trend":"销售趋势","dashboard.analysis.sales-ranking":"门店销售额排名","dashboard.analysis.all-year":"全年","dashboard.analysis.all-month":"本月","dashboard.analysis.all-week":"本周","dashboard.analysis.all-day":"今日","dashboard.analysis.search-users":"搜索用户数","dashboard.analysis.per-capita-search":"人均搜索次数","dashboard.analysis.online-top-search":"线上热门搜索","dashboard.analysis.the-proportion-of-sales":"销售额类别占比","dashboard.analysis.dropdown-option-one":"操作一","dashboard.analysis.dropdown-option-two":"操作二","dashboard.analysis.channel.all":"全部渠道","dashboard.analysis.channel.online":"线上","dashboard.analysis.channel.stores":"门店","dashboard.analysis.sales":"销售额","dashboard.analysis.traffic":"客流量","dashboard.analysis.table.rank":"排名","dashboard.analysis.table.search-keyword":"搜索关键词","dashboard.analysis.table.users":"用户数","dashboard.analysis.table.weekly-range":"周涨幅"}},16771:function(e,t,n){"use strict";n.d(t,{He:function(){return h},Ay:function(){return g}});var a=n(53177),i=n(93251),s=(n(27296),n(9572)),r=n.n(s),o=n(48804),c=n(31041),d=n.n(c),u=n(12700),l={vm:{},install:function(e,t){this.installed||(this.installed=!0,t?(e.axios=t,Object.defineProperties(e.prototype,{axios:{get:function(){return t}},$http:{get:function(){return t}}})):console.error("You have to install axios"))}},m=n(95158),f=r().create({baseURL:"/",headers:{"Content-Type":"application/x-www-form-urlencoded"},timeout:6e3}),p=function(e){if(e.response){var t=e.response.data,n=d().get(m.Xh);403===e.response.status&&u.A.error({message:"Forbidden",description:t.msg}),401!==e.response.status||t.result&&t.result.isLogin||(u.A.error({message:"Unauthorized",description:"Authorization verification failed"}),n&&o.A.dispatch("Logout").then((function(){setTimeout((function(){window.location.reload()}),1500)})))}return Promise.reject(e)};f.interceptors.request.use((function(e){var t=d().get(m.Xh);return t&&(e.headers["admintoken"]=t),e}),p),f.interceptors.response.use(function(){var e=(0,i.A)((0,a.A)().m((function e(t){var n,i,s;return(0,a.A)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t;case 1:return n=e.v,i=n.data,i.code,s=i.msg,"請先登錄，無權限訪問admin"==s?(u.A.error({message:"重新登陆",description:"未登录或登录过期，请重新登录"}),o.A.dispatch("Logout").then((function(){setTimeout((function(){window.localStorage.clear(),window.location.reload()}),1500)}))):n.data||u.A.error({message:"网络错误",description:"网络错误，请稍后刷新页面重试！"}),e.a(2,n.data)}}),e)})));return function(t){return e.apply(this,arguments)}}());var h={vm:{},install:function(e){e.use(l,f)}},g=f},18697:function(e,t,n){"use strict";n.d(t,{$W:function(){return k},CS:function(){return u},Cv:function(){return m},EV:function(){return g},GH:function(){return c},P1:function(){return y},Rr:function(){return h},cD:function(){return N},cG:function(){return l},gs:function(){return w},hS:function(){return C},hj:function(){return j},iA:function(){return o},iE:function(){return v},lu:function(){return b},m:function(){return p},mc:function(){return x},oT:function(){return S},ts:function(){return L},vB:function(){return A},vP:function(){return f},xJ:function(){return I},zl:function(){return d}});var a=n(16771),i=n(85070),s=n.n(i),r={countdata:"/admin/count.do",usermanag:"/admin/user/list.do",useraddSimulatedAccount:"/admin/user/addSimulatedAccount.do",userupdate:"/admin/user/update.do",usergetBank:"/admin/user/getBank.do",userupdateBank:"admin/user/updateBank.do",userupdateAmt:"/admin/user/updateAmt.do",virtualUpdateAmt:"/admin/user/virtualUpdateAmt.do",userauthByAdmin:"/admin/user/authByAdmin.do",userdelete:"/admin/user/delete.do",userexport:"/admin/user/export.do",nextagent:"/admin/agent/list.do",agentupdateAgentAmt:"/admin/agent/updateAgentAmt.do",agentdelAgent:"/admin/agent/delAgent.do",agentupdate:"/admin/agent/update.do",agentadd:"/admin/agent/add.do",stockgetMarket:"/api/stock/getMarket.do",adminsetSiteStyle:"/api/admin/setSiteStyle.do",admingetSiteStyle:"/api/admin/getSiteStyle.do",deleteSignature:"/admin/user/deleteSignature.do",agentTreeList:"/admin/agent/treeList.do",resetUserBank:"/admin/user/resetUserBank.do",resetUserRealName:"/admin/user/resetUserRealName.do",getUserPasswords:"/admin/user/getUserPasswords.do"};function o(e){return(0,a.Ay)({url:r.countdata,method:"get",param:e})}function c(e){return(0,a.Ay)({url:r.getUserPasswords,method:"post",data:s().stringify(e)})}function d(e){return(0,a.Ay)({url:r.resetUserBank,method:"post",data:s().stringify(e)})}function u(e){return(0,a.Ay)({url:r.resetUserRealName,method:"post",data:s().stringify(e)})}function l(e){return(0,a.Ay)({url:r.agentTreeList,method:"get",param:e})}function m(e){return(0,a.Ay)({url:r.usermanag,method:"post",data:s().stringify(e)})}function f(e){return(0,a.Ay)({url:r.nextagent,method:"post",data:s().stringify(e)})}function p(e){return(0,a.Ay)({url:r.useraddSimulatedAccount,method:"post",data:s().stringify(e)})}function h(e){return(0,a.Ay)({url:r.userupdate,method:"post",data:s().stringify(e)})}function g(e){return(0,a.Ay)({url:r.usergetBank,method:"post",data:s().stringify(e)})}function b(e){return(0,a.Ay)({url:r.userupdateBank,method:"post",data:s().stringify(e)})}function y(e){return(0,a.Ay)({url:r.virtualUpdateAmt,method:"post",data:s().stringify(e)})}function k(e){return(0,a.Ay)({url:r.userupdateAmt,method:"post",data:s().stringify(e)})}function v(e){return(0,a.Ay)({url:r.userauthByAdmin,method:"post",data:s().stringify(e)})}function A(e){return(0,a.Ay)({url:r.userdelete,method:"post",data:s().stringify(e)})}function C(e){return(0,a.Ay)({url:r.deleteSignature,method:"post",data:s().stringify(e)})}function w(e){return(0,a.Ay)({url:r.agentupdateAgentAmt,method:"post",data:s().stringify(e)})}function S(e){return(0,a.Ay)({url:r.agentdelAgent,method:"post",data:s().stringify(e)})}function j(e){return(0,a.Ay)({url:r.agentupdate,method:"post",data:s().stringify(e)})}function L(e){return(0,a.Ay)({url:r.agentadd,method:"post",data:s().stringify(e)})}function I(e){return(0,a.Ay)({url:r.stockgetMarket,method:"post",data:s().stringify(e)})}function x(e){return(0,a.Ay)({url:r.adminsetSiteStyle,method:"post",data:s().stringify(e)})}function N(){return(0,a.Ay)({url:r.userexport,method:"post",responseType:"blob"})}},19994:function(e,t,n){"use strict";n.r(t),t["default"]={"result.success.title":"提交成功","result.success.description":"提交结果页用于反馈一系列操作任务的处理结果， 如果仅是简单操作，使用 Message 全局提示反馈即可。 本文字区域可以展示简单的补充说明，如果有类似展示 “单据”的需求，下面这个灰色区域可以呈现比较复杂的内容。","result.success.operate-title":"项目名称","result.success.operate-id":"项目 ID","result.success.principal":"负责人","result.success.operate-time":"生效时间","result.success.step1-title":"创建项目","result.success.step1-operator":"曲丽丽","result.success.step2-title":"部门初审","result.success.step2-operator":"周毛毛","result.success.step2-extra":"催一下","result.success.step3-title":"财务复核","result.success.step4-title":"完成","result.success.btn-return":"返回列表","result.success.btn-project":"查看项目","result.success.btn-print":"打印"}},23493:function(e,t,n){"use strict";n.d(t,{N:function(){return l},Vp:function(){return d},iD:function(){return o},mN:function(){return c},ri:function(){return u}});var a=n(16771),i=n(85070),s=n.n(i),r={Login:"/api/admin/login.do",Logout:"/api/admin/logout.do",ForgePassword:"/auth/forge-password",Register:"/auth/register",twoStepCode:"/auth/2step-code",SendSms:"/account/sms",SendSmsErr:"/account/sms_err",UserInfo:"/api/user/info",UserMenu:"/user/nav"};function o(e){return(0,a.Ay)({url:r.Login,method:"post",data:s().stringify(e)})}function c(e){return(0,a.Ay)({url:r.SendSms,method:"post",data:e})}function d(){return(0,a.Ay)({url:r.UserInfo,method:"get",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(){return(0,a.Ay)({url:r.Logout,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return(0,a.Ay)({url:r.twoStepCode,method:"post",data:e})}},31717:function(e,t,n){"use strict";n.d(t,{Av:function(){return r},Z$:function(){return a},dH:function(){return i},lT:function(){return s}});n(80462);function a(){var e=new Date,t=e.getHours();return t<9?"早上好":t<=11?"上午好":t<=13?"中午好":t<20?"下午好":"晚上好"}function i(){var e=["休息一会儿吧","准备吃什么呢?","要不要打一把 DOTA","我猜你可能累了"],t=Math.floor(Math.random()*e.length);return e[t]}function s(){var e=window.navigator.userAgent,t=function(t){return e.indexOf(t)>=0},n=function(){return"ActiveXObject"in window}();return t("MSIE")||n}function r(e){var t=0;if(!e)return t;for(var n={},a=0;a<e.length;a++)n[e[a]]=(n[e[a]]||0)+1,t+=5/n[e[a]];var i={digits:/\d/.test(e),lower:/[a-z]/.test(e),upper:/[A-Z]/.test(e),nonWords:/\W/.test(e)},s=0;for(var r in i)s+=!0===i[r]?1:0;return t+=10*(s-1),parseInt(t)}},33153:function(e,t,n){"use strict";e.exports=n.p+"img/logo.c47eccef.png"},33784:function(e,t,n){"use strict";n.r(t);var a=n(97139),i=n.n(a),s=n(44084),r=function(e){console.log("options",e);var t={id:"4291d7da9005377ec9aec4a71ea837f",name:"天野远子",username:"admin",password:"",avatar:"/avatar2.jpg",status:1,telephone:"",lastLoginIp:"*************",lastLoginTime:1534837621348,creatorId:"admin",createTime:*************,merchantCode:"TLif2btpzg079h15bk",deleted:0,roleId:"admin",role:{}},n={id:"admin",name:"管理员",describe:"拥有所有权限",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"admin",permissionId:"dashboard",permissionName:"仪表盘",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"userlist",permissionName:"仪表盘",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"exception",permissionName:"异常页面权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"result",permissionName:"结果权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"profile",permissionName:"详细页权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"table",permissionName:"表格权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"form",permissionName:"表单权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"table",permissionName:"桌子管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:null,dataAccess:null}]};return n.permissions.push({roleId:"admin",permissionId:"support",permissionName:"超级模块",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:null,dataAccess:null}),t.role=n,(0,s.cL)(t)},o=function(e){var t=[{name:"dashboard",parentId:0,id:1,meta:{title:"menu.dashboard",icon:"dashboard",show:!0},component:"RouteView",redirect:"/dashboard/workplace"},{name:"workplace",parentId:1,id:7,meta:{title:"menu.dashboard.monitor",show:!0},component:"Workplace"},{name:"monitor",path:"https://www.baidu.com/",parentId:1,id:3,meta:{title:"menu.dashboard.workplace",target:"_blank",show:!0}},{name:"Analysis",parentId:1,id:2,meta:{title:"menu.dashboard.analysis",show:!0},component:"Analysis",path:"/dashboard/analysis"},{name:"Userlist",parentId:1,id:2,meta:{title:"用户管理",show:!0},component:"userlist",path:"/userlist/index"},{name:"form",parentId:0,id:10,meta:{icon:"form",title:"menu.form"},redirect:"/form/base-form",component:"RouteView"},{name:"basic-form",parentId:10,id:6,meta:{title:"menu.form.basic-form"},component:"BasicForm"},{name:"step-form",parentId:10,id:5,meta:{title:"menu.form.step-form"},component:"StepForm"},{name:"advanced-form",parentId:10,id:4,meta:{title:"menu.form.advanced-form"},component:"AdvanceForm"},{name:"list",parentId:0,id:10010,meta:{icon:"table",title:"menu.list",show:!0},redirect:"/list/table-list",component:"RouteView"},{name:"table-list",parentId:10010,id:10011,path:"/list/table-list/:pageNo([1-9]\\d*)?",meta:{title:"menu.list.table-list",show:!0},component:"TableList"},{name:"basic-list",parentId:10010,id:10012,meta:{title:"menu.list.basic-list",show:!0},component:"StandardList"},{name:"card",parentId:10010,id:10013,meta:{title:"menu.list.card-list",show:!0},component:"CardList"},{name:"search",parentId:10010,id:10014,meta:{title:"menu.list.search-list",show:!0},redirect:"/list/search/article",component:"SearchLayout"},{name:"article",parentId:10014,id:10015,meta:{title:"menu.list.search-list.articles",show:!0},component:"SearchArticles"},{name:"project",parentId:10014,id:10016,meta:{title:"menu.list.search-list.projects",show:!0},component:"SearchProjects"},{name:"application",parentId:10014,id:10017,meta:{title:"menu.list.search-list.applications",show:!0},component:"SearchApplications"},{name:"profile",parentId:0,id:10018,meta:{title:"menu.profile",icon:"profile",show:!0},redirect:"/profile/basic",component:"RouteView"},{name:"basic",parentId:10018,id:10019,meta:{title:"menu.profile.basic",show:!0},component:"ProfileBasic"},{name:"advanced",parentId:10018,id:10020,meta:{title:"menu.profile.advanced",show:!0},component:"ProfileAdvanced"},{name:"result",parentId:0,id:10021,meta:{title:"menu.result",icon:"check-circle-o",show:!0},redirect:"/result/success",component:"PageView"},{name:"success",parentId:10021,id:10022,meta:{title:"menu.result.success",hiddenHeaderContent:!0,show:!0},component:"ResultSuccess"},{name:"fail",parentId:10021,id:10023,meta:{title:"menu.result.fail",hiddenHeaderContent:!0,show:!0},component:"ResultFail"},{name:"exception",parentId:0,id:10024,meta:{title:"menu.exception",icon:"warning",show:!0},redirect:"/exception/403",component:"RouteView"},{name:"403",parentId:10024,id:10025,meta:{title:"menu.exception.not-permission",show:!0},component:"Exception403"},{name:"404",parentId:10024,id:10026,meta:{title:"menu.exception.not-find",show:!0},component:"Exception404"},{name:"500",parentId:10024,id:10027,meta:{title:"menu.exception.server-error",show:!0},component:"Exception500"},{name:"account",parentId:0,id:10028,meta:{title:"menu.account",icon:"user",show:!0},redirect:"/account/center",component:"RouteView"},{name:"center",parentId:10028,id:10029,meta:{title:"menu.account.center",show:!0},component:"AccountCenter"},{name:"settings",parentId:10028,id:10030,meta:{title:"menu.account.settings",hideHeader:!0,hideChildren:!0,show:!0},redirect:"/account/settings/basic",component:"AccountSettings"},{name:"BasicSettings",path:"/account/settings/basic",parentId:10030,id:10031,meta:{title:"account.settings.menuMap.basic",show:!1},component:"BasicSetting"},{name:"SecuritySettings",path:"/account/settings/security",parentId:10030,id:10032,meta:{title:"account.settings.menuMap.security",show:!1},component:"SecuritySettings"},{name:"CustomSettings",path:"/account/settings/custom",parentId:10030,id:10033,meta:{title:"account.settings.menuMap.custom",show:!1},component:"CustomSettings"},{name:"BindingSettings",path:"/account/settings/binding",parentId:10030,id:10034,meta:{title:"account.settings.menuMap.binding",show:!1},component:"BindingSettings"},{name:"NotificationSettings",path:"/account/settings/notification",parentId:10030,id:10034,meta:{title:"account.settings.menuMap.notification",show:!1},component:"NotificationSettings"}],n=(0,s.cL)(t);return console.log("json",n),n};i().mock(/\/api\/user\/info/,"get",r),i().mock(/\/api\/user\/nav/,"get",o)},34720:function(e,t,n){"use strict";n.r(t);var a=n(82083),i=n(3586);t["default"]=(0,a.A)({},i["default"])},34935:function(e,t,n){"use strict";n.r(t),t["default"]={"app.setting.pagestyle":"整体风格设置","app.setting.pagestyle.light":"亮色菜单风格","app.setting.pagestyle.dark":"暗色菜单风格","app.setting.pagestyle.realdark":"暗黑模式","app.setting.themecolor":"主题色","app.setting.navigationmode":"导航模式","app.setting.content-width":"内容区域宽度","app.setting.fixedheader":"固定 Header","app.setting.fixedsidebar":"固定侧边栏","app.setting.sidemenu":"侧边菜单布局","app.setting.topmenu":"顶部菜单布局","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.othersettings":"其他设置","app.setting.weakmode":"色弱模式","app.setting.copy":"拷贝设置","app.setting.loading":"加载主题中","app.setting.copyinfo":"拷贝设置成功 src/config/defaultSettings.js","app.setting.production.hint":"","app.setting.themecolor.daybreak":"拂晓蓝","app.setting.themecolor.dust":"薄暮","app.setting.themecolor.volcano":"火山","app.setting.themecolor.sunset":"日暮","app.setting.themecolor.cyan":"明青","app.setting.themecolor.green":"极光绿","app.setting.themecolor.geekblue":"极客蓝","app.setting.themecolor.purple":"酱紫"}},37946:function(e,t){"use strict";t.A={navTheme:"dark",primaryColor:"#1890ff",layout:"sidemenu",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!1,colorWeak:!1,menu:{locale:!0},title:"Stock-Admin",pwa:!1,iconfontUrl:"",production:!1}},39024:function(e,t,n){"use strict";n.d(t,{A:function(){return p}});var a,i,s=n(30395),r=new s.Ay,o=n(82083),c=(n(25303),n(42554),n(96124),n(79207),n(48015),n(27296),n(6866),n(89077),{name:"MultiTab",data:function(){return{fullPathList:[],pages:[],activeKey:"",newTabIndex:0}},created:function(){var e=this;r.$on("open",(function(t){if(!t)throw new Error("multi-tab: open tab ".concat(t," err"));e.activeKey=t})).$on("close",(function(t){t?e.closeThat(t):e.closeThat(e.activeKey)})).$on("rename",(function(t){var n=t.key,a=t.name;console.log("rename",n,a);try{var i=e.pages.find((function(e){return e.path===n}));i.meta.customTitle=a,e.$forceUpdate()}catch(s){}})),this.pages.push(this.$route),this.fullPathList.push(this.$route.fullPath),this.selectedLastPath()},methods:{onEdit:function(e,t){this[t](e)},remove:function(e){this.pages=this.pages.filter((function(t){return t.fullPath!==e})),this.fullPathList=this.fullPathList.filter((function(t){return t!==e})),this.fullPathList.includes(this.activeKey)||this.selectedLastPath()},selectedLastPath:function(){this.activeKey=this.fullPathList[this.fullPathList.length-1]},closeThat:function(e){this.fullPathList.length>1?this.remove(e):this.$message.info("这是最后一个标签了, 无法被关闭")},closeLeft:function(e){var t=this,n=this.fullPathList.indexOf(e);n>0?this.fullPathList.forEach((function(e,a){a<n&&t.remove(e)})):this.$message.info("左侧没有标签")},closeRight:function(e){var t=this,n=this.fullPathList.indexOf(e);n<this.fullPathList.length-1?this.fullPathList.forEach((function(e,a){a>n&&t.remove(e)})):this.$message.info("右侧没有标签")},closeAll:function(e){var t=this,n=this.fullPathList.indexOf(e);this.fullPathList.forEach((function(e,a){a!==n&&t.remove(e)}))},closeMenuClick:function(e,t){this[e](t)},renderTabPaneMenu:function(e){var t=this,n=this.$createElement;return n("a-menu",{on:(0,o.A)({},{click:function(n){var a=n.key;n.item,n.domEvent;t.closeMenuClick(a,e)}})},[n("a-menu-item",{key:"closeThat"},["关闭当前标签"]),n("a-menu-item",{key:"closeRight"},["关闭右侧"]),n("a-menu-item",{key:"closeLeft"},["关闭左侧"]),n("a-menu-item",{key:"closeAll"},["关闭全部"])])},renderTabPane:function(e,t){var n=this.$createElement,a=this.renderTabPaneMenu(t);return n("a-dropdown",{attrs:{overlay:a,trigger:["contextmenu"]}},[n("span",{style:{userSelect:"none"}},[e])])}},watch:{$route:function(e){this.activeKey=e.fullPath,this.fullPathList.indexOf(e.fullPath)<0&&(this.fullPathList.push(e.fullPath),this.pages.push(e))},activeKey:function(e){this.$router.push({path:e})}},render:function(){var e=this,t=arguments[0],n=this.onEdit,a=this.$data.pages,i=a.map((function(n){return t("a-tab-pane",{style:{height:0},attrs:{tab:e.renderTabPane(n.meta.customTitle||n.meta.title,n.fullPath),closable:a.length>1},key:n.fullPath})}));return t("div",{class:"ant-pro-multi-tab"},[t("div",{class:"ant-pro-multi-tab-wrapper"},[t("a-tabs",{attrs:{hideAdd:!0,type:"editable-card",tabBarStyle:{background:"#FFF",margin:0,paddingLeft:"16px",paddingTop:"1px"}},on:(0,o.A)({},{edit:n}),model:{value:e.activeKey,callback:function(t){e.activeKey=t}}},[i])])])}}),d=c,u=n(6367),l=(0,u.A)(d,a,i,!1,null,null,null),m=l.exports,f={open:function(e){r.$emit("open",e)},rename:function(e,t){r.$emit("rename",{key:e,name:t})},closeCurrentPage:function(){this.close()},close:function(e){r.$emit("close",e)}};m.install=function(e){e.prototype.$multiTab||(f.instance=r,e.prototype.$multiTab=f,e.component("multi-tab",m))};var p=m},40975:function(e,t,n){"use strict";n.d(t,{w:function(){return s}});var a=n(82083),i=n(97830),s={computed:(0,a.A)({},(0,i.aH)({isMobile:function(e){return e.app.isMobile}}))}},44084:function(e,t,n){"use strict";n.d(t,{LT:function(){return r},cL:function(){return s},lZ:function(){return o}});var a=n(64268),i=(n(62221),n(80462),n(75379),{message:"",timestamp:0,result:null,code:0}),s=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return i.result=e,void 0!==t&&null!==t&&(i.message=t),void 0!==n&&0!==n&&(i.code=n,i._status=n),null!==s&&"object"===(0,a.A)(s)&&Object.keys(s).length>0&&(i._headers=s),i.timestamp=(new Date).getTime(),i},r=function(e){var t=e.url,n=t.split("?")[1];return n?JSON.parse('{"'+decodeURIComponent(n).replace(/"/g,'\\"').replace(/&/g,'","').replace(/=/g,'":"')+'"}'):{}},o=function(e){return e.body&&JSON.parse(e.body)}},45271:function(e,t,n){"use strict";n.d(t,{J4:function(){return g},vb:function(){return b}});var a=n(82083),i=(n(96124),n(27296),n(83129),n(72),n(30395)),s=n(20268),r=n(31041),o=n.n(r),c=n(46279),d=n.n(c),u=n(13978);i.Ay.use(s.A);var l="en-US",m={"en-US":(0,a.A)({},u["default"])},f=new s.A({silentTranslationWarn:!0,locale:l,fallbackLocale:l,messages:m}),p=[l];function h(e){return f.locale=e,document.querySelector("html").setAttribute("lang",e),e}function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l;return new Promise((function(t){return o().set("lang",e),f.locale!==e?p.includes(e)?t(h(e)):n(5839)("./".concat(e)).then((function(t){var n=t.default;return f.setLocaleMessage(e,n),p.push(e),d().updateLocale(n.momentName,n.momentLocale),h(e)})):t(e)}))}function b(e){return f.t("".concat(e))}t.Ay=f},48152:function(e,t,n){"use strict";n.r(t),t["default"]={"menu.welcome":"欢迎","menu.home":"主页","menu.dashboard":"仪表盘","menu.dashboard.analysis":"分析页","menu.dashboard.monitor":"监控页","menu.dashboard.workplace":"工作台","menu.form":"表单页","menu.form.basic-form":"基础表单","menu.form.step-form":"分步表单","menu.form.step-form.info":"分步表单（填写转账信息）","menu.form.step-form.confirm":"分步表单（确认转账信息）","menu.form.step-form.result":"分步表单（完成）","menu.form.advanced-form":"高级表单","menu.list":"列表页","menu.list.table-list":"查询表格","menu.list.basic-list":"标准列表","menu.list.card-list":"卡片列表","menu.list.search-list":"搜索列表","menu.list.search-list.articles":"搜索列表（文章）","menu.list.search-list.projects":"搜索列表（项目）","menu.list.search-list.applications":"搜索列表（应用）","menu.profile":"详情页","menu.profile.basic":"基础详情页","menu.profile.advanced":"高级详情页","menu.result":"结果页","menu.result.success":"成功页","menu.result.fail":"失败页","menu.exception":"异常页","menu.exception.not-permission":"403","menu.exception.not-find":"404","menu.exception.server-error":"500","menu.exception.trigger":"触发错误","menu.account":"个人页","menu.account.center":"个人中心","menu.account.settings":"个人设置","menu.account.trigger":"触发报错","menu.account.logout":"退出登录"}},48804:function(e,t,n){"use strict";n.d(t,{A:function(){return N}});var a,i=n(30395),s=n(97830),r=n(44105),o=(n(27296),n(31041)),c=n.n(o),d=n(95158),u=n(45271),l={state:{sideCollapsed:!1,isMobile:!1,theme:"dark",layout:"",contentWidth:"",fixedHeader:!1,fixedSidebar:!1,autoHideHeader:!1,color:"",weak:!1,multiTab:!0,lang:"zh-CN",_antLocale:{}},mutations:(a={},(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)(a,d.cf,(function(e,t){e.sideCollapsed=t,c().set(d.cf,t)})),d.nd,(function(e,t){e.isMobile=t})),d.RM,(function(e,t){e.theme=t,c().set(d.RM,t)})),d.yG,(function(e,t){e.layout=t,c().set(d.yG,t)})),d.MV,(function(e,t){e.fixedHeader=t,c().set(d.MV,t)})),d.Fb,(function(e,t){e.fixedSidebar=t,c().set(d.Fb,t)})),d.sl,(function(e,t){e.contentWidth=t,c().set(d.sl,t)})),d.Wb,(function(e,t){e.autoHideHeader=t,c().set(d.Wb,t)})),d.Db,(function(e,t){e.color=t,c().set(d.Db,t)})),d.o6,(function(e,t){e.weak=t,c().set(d.o6,t)})),(0,r.A)((0,r.A)(a,d.$C,(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e.lang=t,e._antLocale=n,c().set(d.$C,t)})),d.jc,(function(e,t){c().set(d.jc,t),e.multiTab=t}))),actions:{setLang:function(e,t){var n=e.commit;return new Promise((function(e,a){n(d.$C,t),(0,u.J4)(t).then((function(){e()})).catch((function(e){a(e)}))}))}}},m=l,f=n(82083),p=(n(79207),n(48015),n(6266)),h=n.n(p),g=n(23493),b=n(31717);c().addPlugin(h());var y={state:{token:"",name:"",welcome:"",avatar:"",roles:[],info:{}},mutations:{SET_TOKEN:function(e,t){e.token=t},SET_NAME:function(e,t){var n=t.name,a=t.welcome;e.name=n,e.welcome=a},SET_AVATAR:function(e,t){e.avatar=t},SET_ROLES:function(e,t){e.roles=t},SET_INFO:function(e,t){e.info=t}},actions:{Login:function(e,t){var n=e.commit;return new Promise((function(e,a){(0,g.iD)(t).then((function(i){var s=i;0==s.status?(c().set(d.Xh,s.data.token,s.data.expireTime),n("SET_TOKEN",s.data.token),window.localStorage.setItem("phones",t.adminPhone),e()):a(s.msg)})).catch((function(e){a(e)}))}))},GetInfo:function(e){var t=e.commit;return new Promise((function(e,n){(0,g.Vp)().then((function(a){var i=a.result;if(i.role&&i.role.permissions.length>0){var s=(0,f.A)({},i.role);s.permissions=i.role.permissions.map((function(e){var t=(0,f.A)((0,f.A)({},e),{},{actionList:(e.actionEntitySet||{}).map((function(e){return e.action}))});return t})),s.permissionList=s.permissions.map((function(e){return e.permissionId})),i.role=s,t("SET_ROLES",s),t("SET_INFO",i),t("SET_NAME",{name:i.name,welcome:(0,b.dH)()}),t("SET_AVATAR",i.avatar),e(i)}else n(new Error("getInfo: roles must be a non-null array !"))})).catch((function(e){n(e)}))}))},Logout:function(e){var t=e.commit,n=e.state;return new Promise((function(e){(0,g.ri)(n.token).then((function(){t("SET_TOKEN",""),t("SET_ROLES",[]),c().remove(d.Xh),e()})).catch((function(e){console.log("logout fail:",e)})).finally((function(){}))}))}}},k=y,v=(n(27609),n(25303),n(96124),n(6866),n(77539)),A=n(88632),C=n.n(A);function w(e,t){return!0}function S(e,t){var n=e.filter((function(e){return!!w(t.permissionList,e)&&(e.children&&e.children.length&&(e.children=S(e.children,t)),!0)}));return n}var j={state:{routers:v.f,addRouters:[]},mutations:{SET_ROUTERS:function(e,t){e.addRouters=t,e.routers=v.f.concat(t)}},actions:{GenerateRoutes:function(e,t){var n=e.commit;return new Promise((function(e){var a=t.role,i=C()(v.y),s=S(i,a);n("SET_ROUTERS",s),e()}))}}},L=j,I={isMobile:function(e){return e.app.isMobile},lang:function(e){return e.app.lang},theme:function(e){return e.app.theme},color:function(e){return e.app.color},token:function(e){return e.user.token},avatar:function(e){return e.user.avatar},nickname:function(e){return e.user.name},welcome:function(e){return e.user.welcome},roles:function(e){return e.user.roles},userInfo:function(e){return e.user.info},addRouters:function(e){return e.permission.addRouters},multiTab:function(e){return e.app.multiTab}},x=I;i.Ay.use(s.Ay);var N=new s.Ay.Store({modules:{app:m,user:k,permission:L},state:{},mutations:{},actions:{},getters:x})},51580:function(e,t,n){"use strict";e.exports=n.p+"media/tksq.6d6b120f.mp3"},56086:function(e,t,n){"use strict";n.r(t);var a=n(82083),i=n(19994),s=n(69161);t["default"]=(0,a.A)((0,a.A)({},i["default"]),s["default"])},62490:function(e,t,n){"use strict";n.r(t);var a=n(97139),i=n.n(a),s=n(44084),r=function(){return(0,s.cL)([{value:9,name:"AntV"},{value:8,name:"F2"},{value:8,name:"G2"},{value:8,name:"G6"},{value:8,name:"DataSet"},{value:8,name:"墨者学院"},{value:6,name:"Analysis"},{value:6,name:"Data Mining"},{value:6,name:"Data Vis"},{value:6,name:"Design"},{value:6,name:"Grammar"},{value:6,name:"Graphics"},{value:6,name:"Graph"},{value:6,name:"Hierarchy"},{value:6,name:"Labeling"},{value:6,name:"Layout"},{value:6,name:"Quantitative"},{value:6,name:"Relation"},{value:6,name:"Statistics"},{value:6,name:"可视化"},{value:6,name:"数据"},{value:6,name:"数据可视化"},{value:4,name:"Arc Diagram"},{value:4,name:"Bar Chart"},{value:4,name:"Canvas"},{value:4,name:"Chart"},{value:4,name:"DAG"},{value:4,name:"DG"},{value:4,name:"Facet"},{value:4,name:"Geo"},{value:4,name:"Line"},{value:4,name:"MindMap"},{value:4,name:"Pie"},{value:4,name:"Pizza Chart"},{value:4,name:"Punch Card"},{value:4,name:"SVG"},{value:4,name:"Sunburst"},{value:4,name:"Tree"},{value:4,name:"UML"},{value:3,name:"Chart"},{value:3,name:"View"},{value:3,name:"Geom"},{value:3,name:"Shape"},{value:3,name:"Scale"},{value:3,name:"Animate"},{value:3,name:"Global"},{value:3,name:"Slider"},{value:3,name:"Connector"},{value:3,name:"Transform"},{value:3,name:"Util"},{value:3,name:"DomUtil"},{value:3,name:"MatrixUtil"},{value:3,name:"PathUtil"},{value:3,name:"G"},{value:3,name:"2D"},{value:3,name:"3D"},{value:3,name:"Line"},{value:3,name:"Area"},{value:3,name:"Interval"},{value:3,name:"Schema"},{value:3,name:"Edge"},{value:3,name:"Polygon"},{value:3,name:"Heatmap"},{value:3,name:"Render"},{value:3,name:"Tooltip"},{value:3,name:"Axis"},{value:3,name:"Guide"},{value:3,name:"Coord"},{value:3,name:"Legend"},{value:3,name:"Path"},{value:3,name:"Helix"},{value:3,name:"Theta"},{value:3,name:"Rect"},{value:3,name:"Polar"},{value:3,name:"Dsv"},{value:3,name:"Csv"},{value:3,name:"Tsv"},{value:3,name:"GeoJSON"},{value:3,name:"TopoJSON"},{value:3,name:"Filter"},{value:3,name:"Map"},{value:3,name:"Pick"},{value:3,name:"Rename"},{value:3,name:"Filter"},{value:3,name:"Map"},{value:3,name:"Pick"},{value:3,name:"Rename"},{value:3,name:"Reverse"},{value:3,name:"sort"},{value:3,name:"Subset"},{value:3,name:"Partition"},{value:3,name:"Imputation"},{value:3,name:"Fold"},{value:3,name:"Aggregate"},{value:3,name:"Proportion"},{value:3,name:"Histogram"},{value:3,name:"Quantile"},{value:3,name:"Treemap"},{value:3,name:"Hexagon"},{value:3,name:"Binning"},{value:3,name:"kernel"},{value:3,name:"Regression"},{value:3,name:"Density"},{value:3,name:"Sankey"},{value:3,name:"Voronoi"},{value:3,name:"Projection"},{value:3,name:"Centroid"},{value:3,name:"H5"},{value:3,name:"Mobile"},{value:3,name:"K线图"},{value:3,name:"关系图"},{value:3,name:"烛形图"},{value:3,name:"股票图"},{value:3,name:"直方图"},{value:3,name:"金字塔图"},{value:3,name:"分面"},{value:3,name:"南丁格尔玫瑰图"},{value:3,name:"饼图"},{value:3,name:"线图"},{value:3,name:"点图"},{value:3,name:"散点图"},{value:3,name:"子弹图"},{value:3,name:"柱状图"},{value:3,name:"仪表盘"},{value:3,name:"气泡图"},{value:3,name:"漏斗图"},{value:3,name:"热力图"},{value:3,name:"玉玦图"},{value:3,name:"直方图"},{value:3,name:"矩形树图"},{value:3,name:"箱形图"},{value:3,name:"色块图"},{value:3,name:"螺旋图"},{value:3,name:"词云"},{value:3,name:"词云图"},{value:3,name:"雷达图"},{value:3,name:"面积图"},{value:3,name:"马赛克图"},{value:3,name:"盒须图"},{value:3,name:"坐标轴"},{value:3,name:""},{value:3,name:"Jacques Bertin"},{value:3,name:"Leland Wilkinson"},{value:3,name:"William Playfair"},{value:3,name:"关联"},{value:3,name:"分布"},{value:3,name:"区间"},{value:3,name:"占比"},{value:3,name:"地图"},{value:3,name:"时间"},{value:3,name:"比较"},{value:3,name:"流程"},{value:3,name:"趋势"},{value:2,name:"亦叶"},{value:2,name:"再飞"},{value:2,name:"完白"},{value:2,name:"巴思"},{value:2,name:"张初尘"},{value:2,name:"御术"},{value:2,name:"有田"},{value:2,name:"沉鱼"},{value:2,name:"玉伯"},{value:2,name:"画康"},{value:2,name:"祯逸"},{value:2,name:"绝云"},{value:2,name:"罗宪"},{value:2,name:"萧庆"},{value:2,name:"董珊珊"},{value:2,name:"陆沉"},{value:2,name:"顾倾"},{value:2,name:"Domo"},{value:2,name:"GPL"},{value:2,name:"PAI"},{value:2,name:"SPSS"},{value:2,name:"SYSTAT"},{value:2,name:"Tableau"},{value:2,name:"D3"},{value:2,name:"Vega"},{value:2,name:"统计图表"}])};i().mock(/\/data\/antv\/tag-cloud/,"get",r)},63060:function(e,t,n){"use strict";n.d(t,{$G:function(){return he},t$:function(){return ge.A},YJ:function(){return j}});var a,i,s=function(){var e=this,t=e._self._c;return t("div",{class:["user-layout-wrapper",e.isMobile&&"mobile"],attrs:{id:"userLayout"}},[t("div",{staticClass:"container"},[t("div",{staticClass:"user-layout-content"},[e._m(0),t("router-view")],1)])])},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"top"},[t("div",{staticClass:"header"},[t("a",{attrs:{href:"/"}},[t("img",{staticClass:"logo",attrs:{src:n(33153),alt:"logo"}}),t("span",{staticClass:"title"},[e._v("Stock-Admin")])])])])}],o=n(40975),c=(n(41683),n(23654)),d=(n(41619),n(87929)),u=(n(98725),n(81587)),l=(n(79207),n(27296),n(45271)),m=n(82083),f=n(97830),p={computed:(0,m.A)({},(0,f.aH)({currentLang:function(e){return e.app.lang}})),methods:{setLang:function(e){this.$store.dispatch("setLang",e)}}},h=p,g=["zh-CN","en-US"],b={"zh-CN":"简体中文","en-US":"English"},y={"zh-CN":"🇨🇳","en-US":"🇺🇸"},k={props:{prefixCls:{type:String,default:"ant-pro-drop-down"}},name:"SelectLang",mixins:[h],render:function(){var e=this,t=arguments[0],n=this.prefixCls,a=function(t){var n=t.key;e.setLang(n)},i=t(u.Ay,{class:["menu","ant-pro-header-menu"],attrs:{selectedKeys:[this.currentLang]},on:{click:a}},[g.map((function(e){return t(u.Ay.Item,{key:e},[t("span",{attrs:{role:"img","aria-label":b[e]}},[y[e]])," ",b[e]])}))]);return t(c.Ay,{attrs:{overlay:i,placement:"bottomRight"}},[t("span",{class:n},[t(d.A,{attrs:{type:"global",title:(0,l.vb)("navBar.lang")}})])])}},v=k,A={name:"UserLayout",components:{SelectLang:v},mixins:[o.w],mounted:function(){document.body.classList.add("userLayout")},beforeDestroy:function(){document.body.classList.remove("userLayout")}},C=A,w=n(6367),S=(0,w.A)(C,s,r,!1,null,"4e3312bc",null),j=S.exports,L=function(){var e=this,t=e._self._c;return t("div",[t("router-view")],1)},I=[],x={name:"BlankLayout"},N=x,T=(0,w.A)(N,L,I,!1,null,"7f25f9eb",null),E=(T.exports,function(){var e=this,t=e._self._c;return t("pro-layout",e._b({attrs:{menus:e.menus,collapsed:e.collapsed,mediaQuery:e.query,isMobile:e.isMobile,handleMediaQuery:e.handleMediaQuery,handleCollapse:e.handleCollapse,i18nRender:e.i18nRender},scopedSlots:e._u([{key:"menuHeaderRender",fn:function(){return[t("div",[t("img",{attrs:{src:n(33153)}}),t("h1",[e._v(e._s(e.title))])])]},proxy:!0},{key:"headerContentRender",fn:function(){return[t("div",[t("a-tooltip",{attrs:{title:"刷新页面"}},[t("a-icon",{staticStyle:{"font-size":"18px",cursor:"pointer"},attrs:{type:"reload"},on:{click:function(t){return e.getreload()}}})],1)],1)]},proxy:!0},{key:"rightContentRender",fn:function(){return[t("right-content",{attrs:{"top-menu":"topmenu"===e.settings.layout,"is-mobile":e.isMobile,theme:e.settings.theme}})]},proxy:!0},{key:"footerRender",fn:function(){return[t("global-footer")]},proxy:!0}])},"pro-layout",e.settings,!1),[t("router-view")],1)}),z=[],P=(n(42554),n(8983)),F=n(95158),M=n(37946),q=function(){var e=this,t=e._self._c;return t("div",{class:e.wrpCls},[t("span",{staticStyle:{"font-size":"18px",color:"red"}},[e._v(e._s(this.orderNmber)+"笔提款未处理")]),t("avatar-dropdown",{class:e.prefixCls,attrs:{menu:e.showMenu,"current-user":e.currentUser}}),t("audio",{ref:"notifyAudio",staticStyle:{display:"none"}},[t("source",{attrs:{src:n(51580),type:"audio/mpeg"}})])],1)},U=[],_=n(53177),O=n(93251),D=n(44105),$=(n(75279),n(48015),function(){var e=this,t=e._self._c;return e.currentUser&&e.currentUser.name?t("a-dropdown",{attrs:{placement:"bottomRight"},scopedSlots:e._u([{key:"overlay",fn:function(){return[t("a-menu",{staticClass:"ant-pro-drop-down menu",attrs:{"selected-keys":[]}},[e.menu?t("a-menu-item",{key:"settings",on:{click:e.handleToSettings}},[t("a-icon",{attrs:{type:"setting"}}),e._v(" "+e._s(e.$t("menu.account.settings"))+" ")],1):e._e(),e.menu?t("a-menu-divider"):e._e(),t("a-menu-item",{key:"logout",on:{click:e.handleLogout}},[t("a-icon",{attrs:{type:"logout"}}),e._v(" "+e._s(e.$t("menu.account.logout"))+" ")],1)],1)]},proxy:!0}],null,!1,**********)},[t("span",{staticClass:"ant-pro-account-avatar"},[t("a-avatar",{staticClass:"antd-pro-global-header-index-avatar",attrs:{size:"small",src:"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png"}}),t("span",[e._v(e._s(e.currentUser.name))])],1)]):t("span",[t("a-spin",{style:{marginLeft:8,marginRight:8},attrs:{size:"small"}})],1)}),R=[],B=(n(75181),n(65905)),V={name:"AvatarDropdown",props:{currentUser:{type:Object,default:function(){return null}},menu:{type:Boolean,default:!0}},methods:{handleToCenter:function(){this.$router.push({path:"/account/center"})},handleToSettings:function(){this.$router.push({path:"/account/settings"})},handleLogout:function(e){var t=this;B.A.confirm({title:this.$t("layouts.usermenu.dialog.title"),content:this.$t("layouts.usermenu.dialog.content"),onOk:function(){return t.$store.dispatch("Logout").then((function(){t.$router.push({name:"login"})}))},onCancel:function(){}})}}},G=V,W=(0,w.A)(G,$,R,!1,null,"0278373c",null),H=W.exports,K=n(91352),X=n(51580),J=n(71008),Y={name:"RightContent",components:{AvatarDropdown:H,SelectLang:v},props:{prefixCls:{type:String,default:"ant-pro-global-header-index-action"},isMobile:{type:Boolean,default:function(){return!1}},topMenu:{type:Boolean,required:!0},theme:{type:String,required:!0}},data:function(){return{showMenu:!0,currentUser:{},notifyAudio:null,notifyMp3:X,tag:"",intervalId:null,orderNmber:"0"}},computed:{wrpCls:function(){return(0,D.A)({"ant-pro-global-header-index-right":!0},"ant-pro-global-header-index-".concat(this.isMobile||!this.topMenu?"light":this.theme),!0)}},mounted:function(){var e=this;this.getnowuser(),this.startPolling();var t=this,n=this.$refs["notifyAudio"];function a(){t.userClick=!0,n&&(n.muted=!1),document.removeEventListener("click",a)}document.addEventListener("click",a),this.$watch("messageNumber",(function(t,a){t-0>a-0&&e.userClick&&n&&n.play()}))},beforeDestroy:function(){this.stopPolling()},created:function(){this.notifyAudio=new Audio(this.notifyMp3),this.getOrderNumber()},methods:{fetchTag:function(){var e=this;return(0,O.A)((0,_.A)().m((function t(){return(0,_.A)().w((function(t){while(1)switch(t.n){case 0:(0,J.i3)({}).then((function(t){console.log(t),t.data>0&&e.userClick&&e.notifyAudio&&e.notifyAudio.play()})).catch((function(e){})),e.getOrderNumber();case 1:return t.a(2)}}),t)})))()},startPolling:function(){this.intervalId=setInterval(this.fetchTag,12e3)},stopPolling:function(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)},getOrderNumber:function(){var e=this;return(0,O.A)((0,_.A)().m((function t(){return(0,_.A)().w((function(t){while(1)switch(t.n){case 0:(0,J.Zt)({}).then((function(t){e.orderNmber=t.data})).catch((function(t){e.orderNmber=0}));case 1:return t.a(2)}}),t)})))()},getnowuser:function(){var e=this;(0,K.YB)().then((function(t){var n=t.data.list.findIndex((function(e){return e.adminPhone==window.localStorage.getItem("phones")}));setTimeout((function(){e.currentUser={name:t.data.list[n].adminName}}),1500)}))}}},Z=Y,Q=(0,w.A)(Z,q,U,!1,null,null,null),ee=Q.exports,te=function(){var e=this,t=e._self._c;return t("global-footer",{staticClass:"footer custom-render",scopedSlots:e._u([{key:"links",fn:function(){},proxy:!0},{key:"copyright",fn:function(){},proxy:!0}])})},ne=[],ae={name:"ProGlobalFooter",components:{GlobalFooter:P.Tn}},ie=ae,se=(0,w.A)(ie,te,ne,!1,null,null,null),re=se.exports,oe="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",ce={props:{isMobile:Boolean},mounted:function(){},methods:{load:function(){if(oe){var e=document.createElement("script");e.id="_adsbygoogle_js",e.src=oe,this.$el.appendChild(e),setTimeout((function(){(window.adsbygoogle||[]).push({})}),2e3)}}},render:function(){}},de=ce,ue=(0,w.A)(de,a,i,!1,null,"a032cdc2",null),le=ue.exports,me={name:"BasicLayout",components:{SettingDrawer:P.G5,RightContent:ee,GlobalFooter:re,Ads:le},data:function(){return{isProPreviewSite:!0,isDev:!0,menus:[],collapsed:!1,title:M.A.title,settings:{layout:M.A.layout,contentWidth:"sidemenu"===M.A.layout?F.OT.Fluid:M.A.contentWidth,theme:M.A.navTheme,primaryColor:M.A.primaryColor,fixedHeader:M.A.fixedHeader,fixSiderbar:M.A.fixSiderbar,colorWeak:M.A.colorWeak,hideHintAlert:!0,hideCopyButton:!1},query:{},isMobile:!1}},computed:(0,m.A)({},(0,f.aH)({mainMenu:function(e){return e.permission.addRouters}})),created:function(){var e=this,t=this.mainMenu.find((function(e){return"/"===e.path}));this.menus=t&&t.children||[],this.$watch("collapsed",(function(){e.$store.commit(F.cf,e.collapsed)})),this.$watch("isMobile",(function(){e.$store.commit(F.nd,e.isMobile)}))},mounted:function(){var e=this,t=navigator.userAgent;t.indexOf("Edge")>-1&&this.$nextTick((function(){e.collapsed=!e.collapsed,setTimeout((function(){e.collapsed=!e.collapsed}),16)})),console.log("production"),(0,P.V_)(this.settings.primaryColor)},methods:{i18nRender:l.vb,getreload:function(){this.$router.go(0)},handleMediaQuery:function(e){this.query=e,!this.isMobile||e["screen-xs"]?!this.isMobile&&e["screen-xs"]&&(this.isMobile=!0,this.collapsed=!1,this.settings.contentWidth=F.OT.Fluid):this.isMobile=!1},handleCollapse:function(e){this.collapsed=e},handleSettingChange:function(e){var t=e.type,n=e.value;switch(console.log("type",t,n),t&&(this.settings[t]=n),t){case"contentWidth":this.settings[t]=n;break;case"layout":"sidemenu"===n?this.settings.contentWidth=F.OT.Fluid:(this.settings.fixSiderbar=!1,this.settings.contentWidth=F.OT.Fixed);break}}}},fe=me,pe=(0,w.A)(fe,E,z,!1,null,null,null),he=pe.exports,ge=n(82445),be=function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("router-view")],1)},ye=[],ke={name:"PageView"},ve=ke,Ae=(0,w.A)(ve,be,ye,!1,null,null,null);Ae.exports},64790:function(e,t,n){"use strict";var a=n(82083),i=(n(27296),n(65905));t.A=function(e){function t(t,n,s){var r=this;if(s=s||{},r&&r._isVue){var o=document.querySelector("body>div[type=dialog]");o||(o=document.createElement("div"),o.setAttribute("type","dialog"),document.body.appendChild(o));var c=function(e,t){if(e instanceof Function){var n=e();n instanceof Promise?n.then((function(e){e&&t()})):n&&t()}else e||t()},d=new e({data:function(){return{visible:!0}},router:r.$router,store:r.$store,mounted:function(){var e=this;this.$on("close",(function(t){e.handleClose()}))},methods:{handleClose:function(){var e=this;c(this.$refs._component.onCancel,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("cancel"),d.$destroy()}))},handleOk:function(){var e=this;c(this.$refs._component.onOK||this.$refs._component.onOk,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("ok"),d.$destroy()}))}},render:function(e){var r=this,o=s&&s.model;o&&delete s.model;var c=Object.assign({},o&&{model:o}||{},{attrs:Object.assign({},(0,a.A)({},s.attrs||s),{visible:this.visible}),on:Object.assign({},(0,a.A)({},s.on||s),{ok:function(){r.handleOk()},cancel:function(){r.handleClose()}})}),d=n&&n.model;d&&delete n.model;var u=Object.assign({},d&&{model:d}||{},{ref:"_component",attrs:Object.assign({},(0,a.A)({},n&&n.attrs||n)),on:Object.assign({},(0,a.A)({},n&&n.on||n))});return e(i.A,c,[e(t,u)])}}).$mount(o)}}Object.defineProperty(e.prototype,"$dialog",{get:function(){return function(){t.apply(this,arguments)}}})}},69157:function(e,t,n){"use strict";n.r(t);var a=n(82083),i=n(22);t["default"]=(0,a.A)({},i["default"])},69161:function(e,t,n){"use strict";n.r(t),t["default"]={"result.fail.error.title":"提交失败","result.fail.error.description":"请核对并修改以下信息后，再重新提交。","result.fail.error.hint-title":"您提交的内容有如下错误：","result.fail.error.hint-text1":"您的账户已被冻结","result.fail.error.hint-btn1":"立即解冻","result.fail.error.hint-text2":"您的账户还不具备申请资格","result.fail.error.hint-btn2":"立即升级","result.fail.error.btn-text":"返回修改"}},70741:function(e,t,n){"use strict";n.r(t);var a=n(97139),i=n.n(a),s=n(44084),r=function(){return(0,s.cL)([{key:"key-01",title:"研发中心",icon:"mail",children:[{key:"key-01-01",title:"后端组",icon:null,group:!0,children:[{key:"key-01-01-01",title:"JAVA",icon:null},{key:"key-01-01-02",title:"PHP",icon:null},{key:"key-01-01-03",title:"Golang",icon:null}]},{key:"key-01-02",title:"前端组",icon:null,group:!0,children:[{key:"key-01-02-01",title:"React",icon:null},{key:"key-01-02-02",title:"Vue",icon:null},{key:"key-01-02-03",title:"Angular",icon:null}]}]},{key:"key-02",title:"财务部",icon:"dollar",children:[{key:"key-02-01",title:"会计核算",icon:null},{key:"key-02-02",title:"成本控制",icon:null},{key:"key-02-03",title:"内部控制",icon:null,children:[{key:"key-02-03-01",title:"财务制度建设",icon:null},{key:"key-02-03-02",title:"会计核算",icon:null}]}]}])},o=function(){return(0,s.cL)({data:[{id:"admin",name:"管理员",describe:"拥有所有权限",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"admin",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["delete","edit"],dataAccess:null},{roleId:"admin",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["query","get","edit","delete"],dataAccess:null},{roleId:"admin",permissionId:"menu",permissionName:"菜单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","import"],dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["query","add","get"],dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["add","get","edit","delete"],dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:["add","get"],dataAccess:null}]},{id:"svip",name:"SVIP",describe:"超级会员",status:1,creatorId:"system",createTime:1532417744846,deleted:0,permissions:[{roleId:"admin",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["add","get","delete"],dataAccess:null},{roleId:"admin",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["add","query","get"],dataAccess:null},{roleId:"admin",permissionId:"menu",permissionName:"菜单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["add","get"],dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","query"],dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","get","edit"],dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:["add","edit"],dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add"],dataAccess:null}]},{id:"user",name:"普通会员",describe:"普通用户，只能查询",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"user",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["query"],dataAccess:null},{roleId:"user",permissionId:"marketing",permissionName:"营销管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"user",permissionId:"menu",permissionName:"菜单管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"user",permissionId:"permission",permissionName:"权限管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"role",permissionName:"角色管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"user",permissionName:"用户管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null}]}],pageSize:10,pageNo:0,totalPage:1,totalCount:5})},c=function(){return(0,s.cL)([{id:"marketing",name:"营销管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:null,parents:null,type:null,deleted:0,actions:["add","query","get","edit","delete"]},{id:"member",name:"会员管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"menu",name:"菜单管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","import","get","edit"]},{id:"order",name:"订单管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"permission",name:"权限管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"role",name:"角色管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"test",name:"测试权限",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]},{id:"user",name:"用户管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"export","defaultCheck":false,"describe":"导出"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]}])},d=function(){return(0,s.cL)({data:[{id:"marketing",name:"营销管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:null,parents:null,type:null,deleted:0,actions:["add","query","get","edit","delete"]},{id:"member",name:"会员管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"menu",name:"菜单管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","import","get","edit"]},{id:"order",name:"订单管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"permission",name:"权限管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"role",name:"角色管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"test",name:"测试权限",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]},{id:"user",name:"用户管理",describe:null,status:1,actionData:'[{"action":"add","describe":"新增","defaultCheck":false},{"action":"get","describe":"查询","defaultCheck":false}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]}],pageSize:10,pageNo:0,totalPage:1,totalCount:5})};i().mock(/\/org\/tree/,"get",r),i().mock(/\/role/,"get",o),i().mock(/\/permission\/no-pager/,"get",c),i().mock(/\/permission/,"get",d)},71008:function(e,t,n){"use strict";n.d(t,{$Q:function(){return b},Be:function(){return C},EL:function(){return c},Fj:function(){return x},I1:function(){return m},LT:function(){return u},M_:function(){return p},ST:function(){return l},VW:function(){return y},Wc:function(){return o},ZB:function(){return h},Zt:function(){return v},_E:function(){return T},dg:function(){return I},dh:function(){return g},i3:function(){return k},mf:function(){return A},oW:function(){return N},oq:function(){return f},ot:function(){return j},pM:function(){return S},q1:function(){return w},rL:function(){return L},xX:function(){return d}});var a=n(16771),i=n(85070),s=n.n(i),r={rechargelist:"/admin/recharge/list.do",rechargedel:"/admin/recharge/del.do",rechargeupdateState:"/admin/recharge/updateState.do",rechargecreateOrder:"/admin/recharge/createOrder.do",rechargeexport:"/admin/recharge/export.do",rechargeCountRechargeAmount:"/admin/recharge/countRechargeAmount.do",withdrawCountRechargeAmount:"/admin/withdraw/countRechargeAmount.do",withdrawlist:"/admin/withdraw/list.do",withdrawupdateState:"admin/withdraw/updateState.do",withdrawexport:"/admin/withdraw/export.do",cashlist:"/admin/cash/list.do",logtransList:"/admin/log/transList.do",getPendingOrderNum:"/admin/withdraw/getPendingOrderNum.do",getOrderNum:"/admin/withdraw/getRealPendingOrderNum.do",fundList:"/admin/user/fund/list.do",getWithdrawableAmount:"/admin/user/fund/withdrawable.do",updateUserFund:"/admin/user/fund/update.do",fundConsumptionList:"/admin/user/fund/consumption/list.do",fundConsumptionBySource:"/admin/user/fund/consumption/byFundSource.do",userFundFlowList:"/admin/user/fund/flow/list.do",queryFundFlowByOrderSn:"/admin/user/fund/flow/queryByOrderSn.do",getChangeTypes:"/admin/user/fund/flow/getChangeTypes.do",rechargeStatistics:"/admin/recharge/rechargeStatistics.do",withdrawStatistics:"/admin/withdraw/withdrawStatistics.do"};function o(e){return(0,a.Ay)({url:r.rechargelist,method:"post",data:s().stringify(e)})}function c(e){return(0,a.Ay)({url:r.withdrawStatistics,method:"post",data:s().stringify(e)})}function d(e){return(0,a.Ay)({url:r.rechargeStatistics,method:"post",data:s().stringify(e)})}function u(e){return(0,a.Ay)({url:r.rechargedel,method:"post",data:s().stringify(e)})}function l(e){return(0,a.Ay)({url:r.rechargeupdateState,method:"post",data:s().stringify(e)})}function m(e){return(0,a.Ay)({url:r.rechargecreateOrder,method:"post",data:s().stringify(e)})}function f(e){return(0,a.Ay)({url:r.rechargeexport,method:"post",responseType:"blob",data:s().stringify(e)})}function p(e){return(0,a.Ay)({url:r.rechargeCountRechargeAmount,method:"post",data:s().stringify(e)})}function h(e){return(0,a.Ay)({url:r.withdrawCountRechargeAmount,method:"post",data:s().stringify(e)})}function g(e){return(0,a.Ay)({url:r.withdrawlist,method:"post",data:s().stringify(e)})}function b(e){return(0,a.Ay)({url:r.withdrawupdateState,method:"post",data:s().stringify(e)})}function y(e){return(0,a.Ay)({url:r.withdrawexport,method:"post",responseType:"blob",data:s().stringify(e)})}function k(e){return(0,a.Ay)({url:r.getPendingOrderNum+"?v="+Date.now(),method:"post",data:s().stringify(e)})}function v(e){return(0,a.Ay)({url:r.getOrderNum+"?v="+Date.now(),method:"post",data:s().stringify(e)})}function A(e){return(0,a.Ay)({url:r.cashlist,method:"post",data:s().stringify(e)})}function C(e){return(0,a.Ay)({url:r.logtransList,method:"post",data:s().stringify(e)})}function w(e){return(0,a.Ay)({url:r.fundList,method:"post",data:s().stringify(e)})}function S(e){return(0,a.Ay)({url:r.getWithdrawableAmount,method:"post",data:s().stringify(e)})}function j(e){return(0,a.Ay)({url:r.updateUserFund,method:"post",data:s().stringify(e)})}function L(e){return(0,a.Ay)({url:r.fundConsumptionList,method:"post",data:s().stringify(e)})}function I(e){return(0,a.Ay)({url:r.fundConsumptionBySource,method:"post",data:s().stringify(e)})}function x(e){return(0,a.Ay)({url:r.userFundFlowList,method:"post",data:s().stringify(e)})}function N(e){return(0,a.Ay)({url:r.queryFundFlowByOrderSn,method:"post",data:s().stringify(e)})}function T(e){return(0,a.Ay)({url:r.getChangeTypes,method:"post",data:s().stringify(e)})}},77539:function(e,t,n){"use strict";n.d(t,{y:function(){return o},f:function(){return c}});n(27296),n(83129),n(72);var a=n(63060),i=n(87885),s=n.n(i),r=n(82445),o=[{path:"/",name:"index",component:a.$G,meta:{title:"menu.home"},redirect:"/dashboard/workplace",children:[{path:"/dashboard",name:"dashboard",redirect:"/dashboard/workplace",component:r.A,meta:{title:"menu.dashboard",keepAlive:!0,icon:s(),permission:["dashboard"]},children:[{path:"/dashboard/workplace",name:"Workplace",component:function(){return n.e(7345).then(n.bind(n,47345))},meta:{title:"menu.dashboard.workplace",keepAlive:!0,permission:["dashboard"]}}]},{path:"/userlist",redirect:"/userlist/index",component:r.A,meta:{title:"用户管理",icon:"usergroup-delete",permission:["userlist"]},children:[{path:"/userlist/index",name:"Userlist",component:function(){return n.e(5753).then(n.bind(n,45753))},meta:{title:"用户列表",keepAlive:!0,permission:["userlist"]}},{path:"/userlist/agentlist",name:"Agentlist",component:function(){return n.e(460).then(n.bind(n,70460))},meta:{title:"代理列表",keepAlive:!0,permission:["agentlist"]}}]},{path:"/product",redirect:"/product/shares",component:r.A,meta:{title:"产品管理",icon:"area-chart",permission:["shares"]},children:[{path:"/product/shares",name:"shares",component:function(){return n.e(9944).then(n.bind(n,79944))},meta:{title:"股票产品",keepAlive:!0,permission:["shares"]}},{path:"/product/index",name:"index",component:function(){return n.e(1969).then(n.bind(n,51969))},meta:{title:"指数产品",keepAlive:!0,permission:["index"]}}]},{path:"/position",redirect:"/position/financing",component:r.A,meta:{title:"持仓管理",icon:"money-collect",permission:["financing"]},children:[{path:"/position/financing",name:"financing",component:function(){return n.e(8953).then(n.bind(n,88953))},meta:{title:"持仓管理",keepAlive:!0,permission:["financing"]}},{path:"/position/createfinancing",name:"createfinancing",component:function(){return n.e(7670).then(n.bind(n,37670))},meta:{title:"创建股票持仓",keepAlive:!0,permission:["createfinancing"]}}]},{path:"/newshares",redirect:"/newshares/newshareslist",component:r.A,meta:{title:"新股管理",icon:"sliders",permission:["newshareslist"]},children:[{path:"/newshares/newshareslist",name:"newshareslist",component:function(){return n.e(2891).then(n.bind(n,72891))},meta:{title:"新股列表",keepAlive:!0,permission:["newshareslist"]}},{path:"/newshares/newsharesrecord",name:"newsharesrecord",component:function(){return n.e(6996).then(n.bind(n,46996))},meta:{title:"新股申购记录",keepAlive:!0,permission:["newsharesrecord"]}},{path:"/newshares/dazonglist",name:"dazonglist",component:function(){return n.e(4338).then(n.bind(n,74338))},meta:{title:"大宗交易列表",keepAlive:!0,permission:["dazonglist"]}}]},{path:"/capital",redirect:"/capital/rechargelist",component:r.A,meta:{title:"资金管理",icon:"dollar",permission:["rechargelist"]},children:[{path:"/capital/rechargelist",name:"rechargelist",component:function(){return n.e(2413).then(n.bind(n,82413))},meta:{title:"充值列表",keepAlive:!0,permission:["rechargelist"]}},{path:"/capital/withdrawallist",name:"withdrawallist",component:function(){return n.e(4464).then(n.bind(n,74464))},meta:{title:"提现列表",keepAlive:!0,permission:["withdrawallist"]}},{path:"/capital/fundrecords",name:"fundrecords",component:function(){return n.e(1322).then(n.bind(n,1322))},meta:{title:"资金记录",keepAlive:!0,permission:["fundrecords"]}},{path:"/capital/fundtransferrecord",name:"fundtransferrecord",component:function(){return n.e(675).then(n.bind(n,60675))},meta:{title:"资金互转记录",keepAlive:!0,permission:["fundtransferrecord"]}},{path:"/capital/fundsSource",name:"fundsSource",component:function(){return n.e(3266).then(n.bind(n,93266))},meta:{title:"可提现资金来源",keepAlive:!0,permission:["fundsSource"]}},{path:"/capital/fundConsumption",name:"fundConsumption",component:function(){return n.e(5086).then(n.bind(n,55086))},meta:{title:"资金消费记录",keepAlive:!0,permission:["fundConsumption"]}},{path:"/capital/userFundFlow",name:"userFundFlow",component:function(){return n.e(214).then(n.bind(n,214))},meta:{title:"用户资金流水",keepAlive:!0,permission:["userFundFlow"]}}]},{path:"/logmanage",redirect:"/logmanage/loginlog",component:r.A,meta:{title:"日志管理",icon:"solution",permission:["loginlog"]},children:[{path:"/logmanage/loginlog",name:"loginlog",component:function(){return n.e(3228).then(n.bind(n,53228))},meta:{title:"登录日志",keepAlive:!0,permission:["loginlog"]}},{path:"/logmanage/smslog",name:"smslog",component:function(){return n.e(1350).then(n.bind(n,71350))},meta:{title:"短信日志",keepAlive:!0,permission:["smslog"]}},{path:"/logmanage/scheduledtasks",name:"scheduledtasks",component:function(){return n.e(4613).then(n.bind(n,14613))},meta:{title:"定时任务",keepAlive:!0,permission:["scheduledtasks"]}},{path:"/logmanage/stationmessage",name:"stationmessage",component:function(){return n.e(5531).then(n.bind(n,85531))},meta:{title:"站内消息",keepAlive:!0,permission:["stationmessage"]}}]},{path:"/managesettings",redirect:"/managesettings/managelist",component:r.A,meta:{title:"管理设置",icon:"control",permission:["managelist"]},children:[{path:"/managesettings/managelist",name:"managelist",component:function(){return n.e(4232).then(n.bind(n,4232))},meta:{title:"管理列表",keepAlive:!0,permission:["managelist"]}}]},{path:"/whitelist",redirect:"/whitelist/whitelist",component:r.A,meta:{title:"白名单设置",icon:"control",permission:["whitelist"]},children:[{path:"/whitelist/whitelist",name:"whitelist",component:function(){return n.e(3172).then(n.bind(n,53172))},meta:{title:"白名单设置",keepAlive:!0,permission:["whitelist"]}}]},{path:"/risksetting",redirect:"/risksetting/productsetting",component:r.A,meta:{title:"风控设置",icon:"warning",permission:["productsetting"]},children:[{path:"/risksetting/productsetting",name:"productsetting",component:function(){return n.e(1200).then(n.bind(n,11200))},meta:{title:"产品配置",keepAlive:!0,permission:["productsetting"]}},{path:"/risksetting/sharessetting",name:"sharessetting",component:function(){return n.e(1028).then(n.bind(n,21028))},meta:{title:"股票风控",keepAlive:!0,permission:["sharessetting"]}},{path:"/risksetting/indexsetting",name:"indexsetting",component:function(){return n.e(4308).then(n.bind(n,94308))},meta:{title:"指数风控",keepAlive:!0,permission:["indexsetting"]}},{path:"/risksetting/spreadsetting",name:"spreadsetting",component:function(){return n.e(6930).then(n.bind(n,36930))},meta:{title:"点差设置",keepAlive:!0,permission:["spreadsetting"]}}]},{path:"/allsetting",redirect:"/allsetting/noticesetting",component:r.A,meta:{title:"系统设置",icon:"setting",permission:["noticesetting"]},children:[{path:"/allsetting/noticesetting",name:"noticesetting",component:function(){return n.e(2889).then(n.bind(n,32889))},meta:{title:"公告设置",keepAlive:!0,permission:["noticesetting"]}},{path:"/allsetting/bannersetting",name:"bannersetting",component:function(){return n.e(7972).then(n.bind(n,77972))},meta:{title:"轮播图设置",keepAlive:!0,permission:["bannersetting"]}},{path:"/allsetting/paysetting",name:"paysetting",component:function(){return n.e(2268).then(n.bind(n,12268))},meta:{title:"支付渠道设置",keepAlive:!0,permission:["paysetting"]}},{path:"/allsetting/platformsetting",name:"platformsetting",component:function(){return n.e(4821).then(n.bind(n,14821))},meta:{title:"平台设置",keepAlive:!0,permission:["platformsetting"]}}]},{path:"/account",component:r.A,redirect:"/account/center",name:"account",meta:{title:"个人中心",icon:"user",keepAlive:!0,permission:["user"]},children:[{path:"/account/settings",name:"settings",component:function(){return n.e(8849).then(n.bind(n,78849))},meta:{title:"menu.account.settings",hideHeader:!0,permission:["user"]},redirect:"/account/settings/basic",hideChildrenInMenu:!0,children:[{path:"/account/settings/basic",name:"BasicSettings",component:function(){return Promise.all([n.e(8616),n.e(3350)]).then(n.bind(n,92830))},meta:{title:"account.settings.menuMap.basic",hidden:!0,permission:["user"]}},{path:"/account/settings/security",name:"SecuritySettings",component:function(){return Promise.all([n.e(8616),n.e(8512)]).then(n.bind(n,83452))},meta:{title:"account.settings.menuMap.security",hidden:!0,keepAlive:!0,permission:["user"]}},{path:"/account/settings/custom",name:"CustomSettings",component:function(){return n.e(966).then(n.bind(n,10966))},meta:{title:"account.settings.menuMap.custom",hidden:!0,keepAlive:!0,permission:["user"]}},{path:"/account/settings/binding",name:"BindingSettings",component:function(){return n.e(1039).then(n.bind(n,1039))},meta:{title:"account.settings.menuMap.binding",hidden:!0,keepAlive:!0,permission:["user"]}},{path:"/account/settings/notification",name:"NotificationSettings",component:function(){return n.e(7465).then(n.bind(n,57465))},meta:{title:"account.settings.menuMap.notification",hidden:!0,keepAlive:!0,permission:["user"]}}]}]}]},{path:"*",redirect:"/404",hidden:!0}],c=[{path:"/user",component:a.YJ,redirect:"/user/login",hidden:!0,children:[{path:"login",name:"login",component:function(){return n.e(6806).then(n.bind(n,90941))}},{path:"register",name:"register",component:function(){return n.e(6806).then(n.bind(n,2577))}},{path:"register-result",name:"registerResult",component:function(){return n.e(6806).then(n.bind(n,53044))}},{path:"recover",name:"recover",component:void 0}]},{path:"/404",component:function(){return n.e(1143).then(n.bind(n,65686))}}]},78152:function(e,t,n){"use strict";n.r(t),t["default"]={submit:"提交",save:"保存","submit.ok":"提交成功","save.ok":"保存成功"}},78232:function(e,t,n){var a={"./af":98271,"./af.js":98271,"./ar":24011,"./ar-dz":76926,"./ar-dz.js":76926,"./ar-kw":75822,"./ar-kw.js":75822,"./ar-ly":26931,"./ar-ly.js":26931,"./ar-ma":65018,"./ar-ma.js":65018,"./ar-ps":68321,"./ar-ps.js":68321,"./ar-sa":42960,"./ar-sa.js":42960,"./ar-tn":8370,"./ar-tn.js":8370,"./ar.js":24011,"./az":73635,"./az.js":73635,"./be":629,"./be.js":629,"./bg":2035,"./bg.js":2035,"./bm":34909,"./bm.js":34909,"./bn":97140,"./bn-bd":14775,"./bn-bd.js":14775,"./bn.js":97140,"./bo":70491,"./bo.js":70491,"./br":14256,"./br.js":14256,"./bs":71655,"./bs.js":71655,"./ca":19696,"./ca.js":19696,"./cs":77482,"./cs.js":77482,"./cv":89717,"./cv.js":89717,"./cy":10536,"./cy.js":10536,"./da":78451,"./da.js":78451,"./de":66807,"./de-at":93653,"./de-at.js":93653,"./de-ch":86315,"./de-ch.js":86315,"./de.js":66807,"./dv":70522,"./dv.js":70522,"./el":62437,"./el.js":62437,"./en-au":83370,"./en-au.js":83370,"./en-ca":28248,"./en-ca.js":28248,"./en-gb":57045,"./en-gb.js":57045,"./en-ie":75538,"./en-ie.js":75538,"./en-il":55737,"./en-il.js":55737,"./en-in":43511,"./en-in.js":43511,"./en-nz":19036,"./en-nz.js":19036,"./en-sg":47934,"./en-sg.js":47934,"./eo":55484,"./eo.js":55484,"./es":69672,"./es-do":22364,"./es-do.js":22364,"./es-mx":90780,"./es-mx.js":90780,"./es-us":56661,"./es-us.js":56661,"./es.js":69672,"./et":413,"./et.js":413,"./eu":74142,"./eu.js":74142,"./fa":30813,"./fa.js":30813,"./fi":19829,"./fi.js":19829,"./fil":15021,"./fil.js":15021,"./fo":37335,"./fo.js":37335,"./fr":78980,"./fr-ca":97717,"./fr-ca.js":97717,"./fr-ch":82430,"./fr-ch.js":82430,"./fr.js":78980,"./fy":92837,"./fy.js":92837,"./ga":74468,"./ga.js":74468,"./gd":92559,"./gd.js":92559,"./gl":63047,"./gl.js":63047,"./gom-deva":94242,"./gom-deva.js":94242,"./gom-latn":14253,"./gom-latn.js":14253,"./gu":88616,"./gu.js":88616,"./he":95571,"./he.js":95571,"./hi":2079,"./hi.js":2079,"./hr":80698,"./hr.js":80698,"./hu":58211,"./hu.js":58211,"./hy-am":43958,"./hy-am.js":43958,"./id":88713,"./id.js":88713,"./is":1676,"./is.js":1676,"./it":76857,"./it-ch":67717,"./it-ch.js":67717,"./it.js":76857,"./ja":62625,"./ja.js":62625,"./jv":95556,"./jv.js":95556,"./ka":59864,"./ka.js":59864,"./kk":55098,"./kk.js":55098,"./km":53852,"./km.js":53852,"./kn":18277,"./kn.js":18277,"./ko":87558,"./ko.js":87558,"./ku":84308,"./ku-kmr":54019,"./ku-kmr.js":54019,"./ku.js":84308,"./ky":70944,"./ky.js":70944,"./lb":19262,"./lb.js":19262,"./lo":28697,"./lo.js":28697,"./lt":90264,"./lt.js":90264,"./lv":79234,"./lv.js":79234,"./me":95446,"./me.js":95446,"./mi":72274,"./mi.js":72274,"./mk":44456,"./mk.js":44456,"./ml":63805,"./ml.js":63805,"./mn":83547,"./mn.js":83547,"./mr":61415,"./mr.js":61415,"./ms":37264,"./ms-my":10923,"./ms-my.js":10923,"./ms.js":37264,"./mt":25829,"./mt.js":25829,"./my":25794,"./my.js":25794,"./nb":42652,"./nb.js":42652,"./ne":17289,"./ne.js":17289,"./nl":97986,"./nl-be":55206,"./nl-be.js":55206,"./nl.js":97986,"./nn":67896,"./nn.js":67896,"./oc-lnc":12516,"./oc-lnc.js":12516,"./pa-in":35067,"./pa-in.js":35067,"./pl":19596,"./pl.js":19596,"./pt":8740,"./pt-br":74709,"./pt-br.js":74709,"./pt.js":8740,"./ro":49515,"./ro.js":49515,"./ru":68341,"./ru.js":68341,"./sd":16499,"./sd.js":16499,"./se":11276,"./se.js":11276,"./si":95336,"./si.js":95336,"./sk":5746,"./sk.js":5746,"./sl":27483,"./sl.js":27483,"./sq":5424,"./sq.js":5424,"./sr":55785,"./sr-cyrl":97e3,"./sr-cyrl.js":97e3,"./sr.js":55785,"./ss":85274,"./ss.js":85274,"./sv":88101,"./sv.js":88101,"./sw":57382,"./sw.js":57382,"./ta":34979,"./ta.js":34979,"./te":58311,"./te.js":58311,"./tet":75931,"./tet.js":75931,"./tg":9641,"./tg.js":9641,"./th":69732,"./th.js":69732,"./tk":94317,"./tk.js":94317,"./tl-ph":74837,"./tl-ph.js":74837,"./tlh":83378,"./tlh.js":83378,"./tr":95222,"./tr.js":95222,"./tzl":91512,"./tzl.js":91512,"./tzm":25871,"./tzm-latn":79129,"./tzm-latn.js":79129,"./tzm.js":25871,"./ug-cn":84536,"./ug-cn.js":84536,"./uk":10784,"./uk.js":10784,"./ur":18223,"./ur.js":18223,"./uz":88711,"./uz-latn":88241,"./uz-latn.js":88241,"./uz.js":88711,"./vi":22853,"./vi.js":22853,"./x-pseudo":36469,"./x-pseudo.js":36469,"./yo":51176,"./yo.js":51176,"./zh-cn":75202,"./zh-cn.js":75202,"./zh-hk":1294,"./zh-hk.js":1294,"./zh-mo":97379,"./zh-mo.js":97379,"./zh-tw":44790,"./zh-tw.js":44790};function i(e){var t=s(e);return n(t)}function s(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}i.keys=function(){return Object.keys(a)},i.resolve=s,e.exports=i,i.id=78232},78287:function(e,t,n){"use strict";n(11755),n(99123),n(39572),n(42218),n(46556),n(14148),n(92405),n(35772),n(51617),n(80898),n(96115),n(38862),n(14664),n(91412),n(87354),n(95981),n(79890),n(61973),n(27609),n(30562),n(21472),n(25303),n(42554),n(75279),n(7114),n(83997),n(98469),n(96124),n(32429),n(79207),n(29916),n(20523),n(35657),n(7427),n(77913),n(82873),n(86806),n(22108),n(8562),n(15338),n(7057),n(17644),n(48015),n(32584),n(49820),n(17742),n(99909),n(54230),n(7841),n(77042),n(32739),n(13309),n(32356),n(6838),n(63669),n(53447),n(72199),n(41618),n(52371),n(10876),n(49927),n(86244),n(28126),n(47875),n(38469),n(5599),n(99756),n(1327),n(54998),n(60072),n(51434),n(59707),n(15964),n(76817),n(17424),n(15540),n(34593),n(41942),n(69848),n(16212),n(53499),n(1577),n(83198),n(15413),n(57291),n(48688),n(44466),n(62221),n(59779),n(36967),n(42770),n(89920),n(88330),n(27296),n(74875),n(87513),n(90588),n(99902),n(61342),n(19077),n(91916),n(56342),n(6473),n(25619),n(27994),n(29914),n(50049),n(34210),n(54615),n(80462),n(9124),n(24362),n(29662),n(17911),n(21030),n(19290),n(6866),n(83129),n(3110),n(38122),n(99127),n(57341),n(65464),n(75379),n(89967),n(9755),n(71491),n(13775),n(80619),n(30262),n(56470),n(4453),n(76181),n(99412),n(16937),n(52635),n(89943),n(32974),n(82859),n(43424),n(71177),n(38321),n(63903),n(15851),n(91556),n(84289),n(91578),n(66364),n(68384),n(93283),n(29617),n(94580),n(42607),n(52331),n(23039),n(9604),n(48117),n(32680),n(83737),n(24782),n(37867),n(90952),n(7148),n(13506),n(22181),n(18202),n(77511),n(45924),n(2803),n(83678),n(54944),n(99730),n(75938),n(16362),n(41289),n(87466),n(99937),n(32035),n(85133),n(89077),n(72),n(26359),n(19457),n(59431),n(57847),n(24723),n(82987);var a=n(31717);(0,a.lT)()&&console.error("[antd-pro] ERROR: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV."),console.log("[antd-pro] mock mounting");var i=n(97139);n(91977),n(33784),n(92244),n(70741),n(62490),n(96029),i.setup({timeout:800}),console.log("[antd-pro] mock mounted");var s=n(30395),r=function(){var e=this,t=e._self._c;return t("a-config-provider",{attrs:{locale:e.locale}},[t("div",{attrs:{id:"app"}},[t("router-view")],1)])},o=[],c=n(37946),d=function(e){document.title=e;var t=navigator.userAgent,n=/\bMicroMessenger\/([\d\.]+)/;if(n.test(t)&&/ip(hone|od|ad)/i.test(t)){var a=document.createElement("iframe");a.src="/favicon.ico",a.style.display="none",a.onload=function(){setTimeout((function(){a.remove()}),9)},document.body.appendChild(a)}},u=c.A.title,l=n(45271),m=n(18697),f={data:function(){return{}},computed:{locale:function(){var e=this.$route.meta.title;return e&&d("".concat((0,l.vb)(e)," - ").concat(u)),this.$i18n.getLocaleMessage(this.$store.getters.lang).antLocale}},created:function(){},methods:{geta:function(){var e=this,t=JSON.parse(localStorage.getItem("theme_save"));t.id=1,(0,m.mc)(t).then((function(e){})),setTimeout((function(){e.geta()}),5e3)}}},p=f,h=n(6367),g=(0,h.A)(p,r,o,!1,null,null,null),b=g.exports,y=n(47677),k=n(77539),v=y.Ay.prototype.push;y.Ay.prototype.push=function(e,t,n){return t||n?v.call(this,e,t,n):v.call(this,e).catch((function(e){return e}))},s.Ay.use(y.Ay);var A=function(){return new y.Ay({mode:"hash",routes:k.f})},C=A();function w(){var e=A();C.matcher=e.matcher}var S=C,j=n(48804),L=n(16771),I=n(8983),x={theme:[{key:"dark",fileName:"dark.css",theme:"dark"},{key:"#F5222D",fileName:"#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",fileName:"#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",fileName:"#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",fileName:"#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",fileName:"#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",fileName:"#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",fileName:"#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}},{key:"#F5222D",theme:"dark",fileName:"dark-#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",theme:"dark",fileName:"dark-#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",theme:"dark",fileName:"dark-#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",theme:"dark",fileName:"dark-#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",theme:"dark",fileName:"dark-#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",theme:"dark",fileName:"dark-#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",theme:"dark",fileName:"dark-#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}}]},N=n(31041),T=n.n(N),E=n(95158),z=function(){console.log("[antd pro] created()");"\n █████╗ ███╗   ██╗████████╗██████╗     ██████╗ ██████╗  ██████╗ \n██╔══██╗████╗  ██║╚══██╔══╝██╔══██╗    ██╔══██╗██╔══██╗██╔═══██╗\n███████║██╔██╗ ██║   ██║   ██║  ██║    ██████╔╝██████╔╝██║   ██║\n██╔══██║██║╚██╗██║   ██║   ██║  ██║    ██╔═══╝ ██╔══██╗██║   ██║\n██║  ██║██║ ╚████║   ██║   ██████╔╝    ██║     ██║  ██║╚██████╔╝\n╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═════╝     ╚═╝     ╚═╝  ╚═╝ ╚═════╝ \n\t\t\t\t\tPublished ".concat("3.0.4","-").concat("f3c80b86"," @ antdv.com\n\t\t\t\t\tBuild date: ").concat("2025/8/4 17:06:41")};function P(){z(),j.A.commit(E.yG,T().get(E.yG,c.A.layout)),j.A.commit(E.MV,T().get(E.MV,c.A.fixedHeader)),j.A.commit(E.Fb,T().get(E.Fb,c.A.fixSiderbar)),j.A.commit(E.sl,T().get(E.sl,c.A.contentWidth)),j.A.commit(E.Wb,T().get(E.Wb,c.A.autoHideHeader)),j.A.commit(E.RM,T().get(E.RM,c.A.navTheme)),j.A.commit(E.o6,T().get(E.o6,c.A.colorWeak)),j.A.commit(E.Db,T().get(E.Db,c.A.primaryColor)),j.A.commit(E.jc,T().get(E.jc,c.A.multiTab)),j.A.commit("SET_TOKEN",T().get(E.Xh)),j.A.dispatch("setLang",T().get(E.$C,"en-US"))}n(83205);var F=n(12700),M=(n(48147),n(61606)),q=(n(86428),n(87259)),U=(n(62357),n(75923)),_=(n(92624),n(95547)),O=(n(30731),n(81080)),D=(n(88375),n(69386)),$=(n(84220),n(22682)),R=(n(87597),n(73406)),B=(n(3333),n(90038)),V=(n(17855),n(79509)),G=(n(58444),n(13977)),W=(n(93573),n(27240)),H=(n(68217),n(48640)),K=(n(16110),n(93065)),X=(n(93831),n(80115)),J=(n(8643),n(82299)),Y=(n(91331),n(2007)),Z=(n(98725),n(81587)),Q=(n(18804),n(3571)),ee=(n(75847),n(73005)),te=(n(32271),n(75750)),ne=(n(57051),n(17950)),ae=(n(80896),n(93645)),ie=(n(41683),n(23654)),se=(n(84837),n(50396)),re=(n(66835),n(82274)),oe=(n(41619),n(87929)),ce=(n(52440),n(71573)),de=(n(32354),n(5741)),ue=(n(75181),n(65905)),le=(n(15025),n(61071)),me=(n(75016),n(10497)),fe=(n(44366),n(99532)),pe=(n(72074),n(91517)),he=(n(39256),n(10283)),ge=(n(57907),n(98304)),be=(n(82167),n(34526)),ye=(n(7480),n(74552)),ke=(n(59144),n(8573)),ve=(n(57138),n(46495)),Ae=(n(10790),n(3764)),Ce=(n(35326),n(74253)),we=(n(17558),n(8394)),Se=n(34551),je=n(50371),Le=n.n(je),Ie=n(64790),xe=n(39024),Ne=n(82083),Te={name:"PageLoading",props:{tip:{type:String,default:"Loading.."},size:{type:String,default:"large"}},render:function(){var e=arguments[0],t={textAlign:"center",background:"rgba(0,0,0,0.6)",position:"fixed",top:0,bottom:0,left:0,right:0,zIndex:1100},n={position:"absolute",left:"50%",top:"40%",transform:"translate(-50%, -50%)"};return e("div",{style:t},[e(Q.A,{attrs:{size:this.size,tip:this.tip},style:n})])}},Ee="0.0.1",ze={newInstance:function(e,t){var n=document.querySelector("body>div[type=loading]");n||(n=document.createElement("div"),n.setAttribute("type","loading"),n.setAttribute("class","ant-loading-wrapper"),document.body.appendChild(n));var a=Object.assign({visible:!1,size:"large",tip:"Loading..."},t),i=new e({data:function(){return(0,Ne.A)({},a)},render:function(){var e=arguments[0],t=this.tip,n={};return this.tip&&(n.tip=t),this.visible?e(Te,{props:(0,Ne.A)({},n)}):null}}).$mount(n);function s(e){var t=(0,Ne.A)((0,Ne.A)({},a),e),n=t.visible,s=t.size,r=t.tip;i.$set(i,"visible",n),r&&i.$set(i,"tip",r),s&&i.$set(i,"size",s)}return{instance:i,update:s}}},Pe={show:function(e){this.instance.update((0,Ne.A)((0,Ne.A)({},e),{},{visible:!0}))},hide:function(){this.instance.update({visible:!1})}},Fe=function(e,t){e.prototype.$loading||(Pe.instance=ze.newInstance(e,t),e.prototype.$loading=Pe)},Me={version:Ee,install:Fe},qe=n(87074),Ue={add:{key:"add",label:"新增"},delete:{key:"delete",label:"删除"},edit:{key:"edit",label:"修改"},query:{key:"query",label:"查询"},get:{key:"get",label:"详情"},enable:{key:"enable",label:"启用"},disable:{key:"disable",label:"禁用"},import:{key:"import",label:"导入"},export:{key:"export",label:"导出"}};function _e(e){_e.installed||(!e.prototype.$auth&&Object.defineProperties(e.prototype,{$auth:{get:function(){var e=this;return function(t){var n=t.split("."),a=(0,qe.A)(n,2),i=a[0],s=a[1],r=e.$store.getters.roles.permissions;return r.find((function(e){return e.permissionId===i})).actionList.findIndex((function(e){return e===s}))>-1}}}}),!e.prototype.$enum&&Object.defineProperties(e.prototype,{$enum:{get:function(){return function(e){var t=Ue;return e&&e.split(".").forEach((function(e){t=t&&t[e]||null})),t}}}}))}var Oe=_e;s.Ay.directive("action",{inserted:function(e,t,n){var a=t.arg,i=j.A.getters.roles,s=n.context.$route.meta.permission,r="[object String]"===Object.prototype.toString.call(s)&&[s]||s;i.permissions.forEach((function(t){r.includes(t.permissionId)&&t.actionList&&!t.actionList.includes(a)&&(e.parentNode&&e.parentNode.removeChild(e)||(e.style.display="none"))}))}});s.Ay.use(we.A),s.Ay.use(Ce.A),s.Ay.use(Ae.A),s.Ay.use(ve.A),s.Ay.use(ke.A),s.Ay.use(ye.A),s.Ay.use(be.Ay),s.Ay.use(ge.A),s.Ay.use(he.Ay),s.Ay.use(pe.A),s.Ay.use(fe.Ay),s.Ay.use(me.A),s.Ay.use(le.A),s.Ay.use(ue.A),s.Ay.use(de.A),s.Ay.use(ce.Ay),s.Ay.use(oe.A),s.Ay.use(re.A),s.Ay.use(se.A),s.Ay.use(ie.Ay),s.Ay.use(ae.Ay),s.Ay.use(ne.A),s.Ay.use(te.A),s.Ay.use(ee.A),s.Ay.use(Q.A),s.Ay.use(Z.Ay),s.Ay.use(Y.A),s.Ay.use(J.A),s.Ay.use(X.A),s.Ay.use(K.A),s.Ay.use(H.A),s.Ay.use(W.A),s.Ay.use(G.Ay),s.Ay.use(V.Ay),s.Ay.use(B.A),s.Ay.use(R.A),s.Ay.use($.A),s.Ay.use(D.A),s.Ay.use(O.Ay),s.Ay.use(_.A),s.Ay.use(U.Ay),s.Ay.use(q.Ay),s.Ay.prototype.$confirm=ue.A.confirm,s.Ay.prototype.$message=M.A,s.Ay.prototype.$notification=F.A,s.Ay.prototype.$info=ue.A.info,s.Ay.prototype.$success=ue.A.success,s.Ay.prototype.$error=ue.A.error,s.Ay.prototype.$warning=ue.A.warning,s.Ay.use(Se.Ay),s.Ay.use(Ie.A),s.Ay.use(xe.A),s.Ay.use(Me),s.Ay.use(Oe),s.Ay.use(Le());var De=n(88990),$e=n.n(De);$e().configure({showSpinner:!1});var Re=["login","register","registerResult"],Be="/user/login",Ve="/dashboard/workplace";S.beforeEach((function(e,t,n){$e().start(),e.meta&&"undefined"!==typeof e.meta.title&&d("".concat((0,l.vb)(e.meta.title)," - ").concat(u));var a=T().get(E.Xh);a?e.path===Be?(n({path:Ve}),$e().done()):0===j.A.getters.roles.length?j.A.dispatch("GetInfo").then((function(i){console.log("res",i),j.A.dispatch("GenerateRoutes",(0,Ne.A)({token:a},i)).then((function(){w(),j.A.getters.addRouters.forEach((function(e){S.addRoute(e)}));var a=decodeURIComponent(t.query.redirect||e.path);e.path===a?n((0,Ne.A)((0,Ne.A)({},e),{},{replace:!0})):n({path:a})}))})).catch((function(){F.A.error({message:"错误",description:"请求用户信息失败，请重试"}),j.A.dispatch("Logout").then((function(){n({path:Be,query:{redirect:e.fullPath}})}))})):n():Re.includes(e.name)?n():(n({path:Be,query:{redirect:e.fullPath}}),$e().done())})),S.afterEach((function(){$e().done()}));var Ge=n(46279),We=n.n(Ge);n(75202);We().locale("zh-cn"),s.Ay.filter("NumberFormat",(function(e){if(!e)return"0";var t=e.toString().replace(/(\d)(?=(?:\d{3})+$)/g,"$1,");return t})),s.Ay.filter("dayjs",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return We()(e).format(t)})),s.Ay.filter("moment",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return We()(e).format(t)})),s.Ay.config.productionTip=!1,s.Ay.prototype.$host="/",s.Ay.use(L.He),s.Ay.component("pro-layout",I.Ay),s.Ay.component("page-container",I.sm),s.Ay.component("page-header-wrapper",I.sm),window.umi_plugin_ant_themeVar=x.theme,new s.Ay({router:S,store:j.A,i18n:l.Ay,created:P,render:function(e){return e(b)}}).$mount("#app")},82445:function(e,t,n){"use strict";n.d(t,{A:function(){return d}});var a,i,s={name:"RouteView",props:{keepAlive:{type:Boolean,default:!0}},data:function(){return{}},render:function(){var e=arguments[0],t=this.$route.meta,n=this.$store.getters,a=e("keep-alive",[e("router-view")]),i=e("router-view");return(n.multiTab||t.keepAlive)&&(this.keepAlive||n.multiTab||t.keepAlive)?a:i}},r=s,o=n(6367),c=(0,o.A)(r,a,i,!1,null,null,null),d=c.exports},87885:function(e,t,n){var a=n(4869)["default"],i=n(63667)["default"],s=["class","staticClass","style","staticStyle","attrs"];n(27609),e.exports={functional:!0,render:function(e,t){var n=t._c,r=(t._v,t.data),o=t.children,c=void 0===o?[]:o,d=r.class,u=r.staticClass,l=r.style,m=r.staticStyle,f=r.attrs,p=void 0===f?{}:f,h=i(r,s);return n("svg",a({class:["bx-analyse_svg__icon",d,u],style:[l,m],attrs:Object.assign({viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg",width:"200",height:"200"},p)},h),c.concat([n("defs"),n("path",{attrs:{d:"M85.333 512h85.334a340.736 340.736 0 0199.712-241.621 337.493 337.493 0 01108.458-72.96 346.453 346.453 0 01261.547-1.75 106.155 106.155 0 00106.283 102.998c59.136 0 106.666-47.531 106.666-106.667S805.803 85.333 746.667 85.333c-29.398 0-55.979 11.776-75.222 30.934-103.722-41.515-222.848-40.875-325.76 2.517a423.595 423.595 0 00-135.68 91.264A423.253 423.253 0 00118.7 345.685 426.88 426.88 0 0085.333 512zm741.248 133.205c-17.109 40.619-41.685 77.142-72.96 108.416s-67.797 55.851-108.458 72.96a346.453 346.453 0 01-261.547 1.75 106.155 106.155 0 00-106.283-102.998c-59.136 0-106.666 47.531-106.666 106.667s47.53 106.667 106.666 106.667c29.398 0 55.979-11.776 75.222-30.934A425.173 425.173 0 00512 938.667a425.941 425.941 0 00393.259-260.352A426.325 426.325 0 00938.667 512h-85.334a341.035 341.035 0 01-26.752 133.205z"}}),n("path",{attrs:{d:"M512 318.379c-106.752 0-193.621 86.869-193.621 193.621S405.248 705.621 512 705.621 705.621 618.752 705.621 512 618.752 318.379 512 318.379zm0 301.909c-59.69 0-108.288-48.597-108.288-108.288S452.309 403.712 512 403.712 620.288 452.309 620.288 512 571.691 620.288 512 620.288z"}})]))}}},89183:function(e,t,n){"use strict";n.r(t);var a=n(82083),i=n(15344);t["default"]=(0,a.A)({},i["default"])},91352:function(e,t,n){"use strict";n.d(t,{T7:function(){return u},VT:function(){return l},Xk:function(){return d},YB:function(){return o},_y:function(){return m},l7:function(){return f},so:function(){return c}});var a=n(16771),i=n(85070),s=n.n(i),r={adminlist:"/admin/list.do",adminupdateLock:"/admin/updateLock.do",adminadd:"/admin/add.do",adminupdate:"/admin/update.do",adminGetGoogle:"/admin/getGoogle.do",adminBindGoogle:"/admin/bindGoogle.do",adminUnbindGoogle:"/admin/unbindGoogle.do"};function o(e){return(0,a.Ay)({url:r.adminlist,method:"post",data:s().stringify(e)})}function c(e){return(0,a.Ay)({url:r.adminupdateLock,method:"post",data:s().stringify(e)})}function d(e){return(0,a.Ay)({url:r.adminadd,method:"post",data:s().stringify(e)})}function u(e){return(0,a.Ay)({url:r.adminupdate,method:"post",data:s().stringify(e)})}function l(e){return(0,a.Ay)({url:r.adminGetGoogle,method:"post",data:s().stringify(e)})}function m(e){return(0,a.Ay)({url:r.adminBindGoogle,method:"post",data:s().stringify(e)})}function f(e){return(0,a.Ay)({url:r.adminUnbindGoogle,method:"post",data:s().stringify(e)})}},91977:function(e,t,n){"use strict";n.r(t);n(96124);var a=n(97139),i=n.n(a),s=n(44084),r=["admin","super"],o=["8914de686ab28dc22f30d3d8e107ff6c","21232f297a57a5a743894a0e4a801fc3"],c=function(e){var t=(0,s.lZ)(e);return console.log("mock: body",t),r.includes(t.username)&&o.includes(t.password)?(0,s.cL)({id:i().mock("@guid"),name:i().mock("@name"),username:"admin",password:"",avatar:"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png",status:1,telephone:"",lastLoginIp:"*************",lastLoginTime:1534837621348,creatorId:"admin",createTime:*************,deleted:0,roleId:"admin",lang:"zh-CN",token:"4291d7da9005377ec9aec4a71ea837f"},"",200,{"Custom-Header":i().mock("@guid")}):(0,s.cL)({isLogin:!0},"账户或密码错误",401)},d=function(){return(0,s.cL)({},"[测试接口] 注销成功")},u=function(){return(0,s.cL)({captcha:i().mock("@integer(10000, 99999)")})},l=function(){return(0,s.cL)({stepCode:i().mock("@integer(0, 1)")})};i().mock(/\/auth\/login/,"post",c),i().mock(/\/auth\/logout/,"post",d),i().mock(/\/account\/sms/,"post",u),i().mock(/\/auth\/2step-code/,"post",l)},92244:function(e,t,n){"use strict";n.r(t);var a=n(97139),i=n.n(a),s=n(44084),r=5701,o=function(e){for(var t=(0,s.LT)(e),n=[],a=parseInt(t.pageNo),o=parseInt(t.pageSize),c=Math.ceil(r/o),d=(a-1)*o,u=(a>=c?r%o:o)+1,l=1;l<u;l++){var m=d+l;n.push({key:m,id:m,no:"No "+m,description:"这是一段描述",callNo:i().mock("@integer(1, 999)"),status:i().mock("@integer(0, 3)"),updatedAt:i().mock("@datetime"),editable:!1})}return(0,s.cL)({pageSize:o,pageNo:a,totalCount:r,totalPage:c,data:n})},c=function(){return(0,s.cL)({data:[{id:1,cover:"https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png",title:"Alipay",description:"那是一种内在的东西， 他们到达不了，也无法触及的",status:1,updatedAt:"2018-07-26 00:00:00"},{id:2,cover:"https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png",title:"Angular",description:"希望是一个好东西，也许是最好的，好东西是不会消亡的",status:1,updatedAt:"2018-07-26 00:00:00"},{id:3,cover:"https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png",title:"Stock Admin",description:"城镇中有那么多的酒馆，她却偏偏走进了我的酒馆",status:1,updatedAt:"2018-07-26 00:00:00"},{id:4,cover:"https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png",title:"Stock Admin",description:"那时候我只会想自己想要什么，从不想自己拥有什么",status:1,updatedAt:"2018-07-26 00:00:00"},{id:5,cover:"https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png",title:"Bootstrap",description:"凛冬将至",status:1,updatedAt:"2018-07-26 00:00:00"},{id:6,cover:"https://gw.alipayobjects.com/zos/rmsportal/ComBAopevLwENQdKWiIn.png",title:"Vue",description:"生命就像一盒巧克力，结果往往出人意料",status:1,updatedAt:"2018-07-26 00:00:00"}],pageSize:10,pageNo:0,totalPage:6,totalCount:57})},d=function(){return(0,s.cL)([{id:1,user:{nickname:"@name",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},project:{name:"白鹭酱油开发组",action:"更新",event:"番组计划"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"蓝莓酱",avatar:"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png"},project:{name:"白鹭酱油开发组",action:"更新",event:"番组计划"},time:"2018-08-23 09:35:37"},{id:1,user:{nickname:"@name",avatar:"@image(64x64)"},project:{name:"白鹭酱油开发组",action:"创建",event:"番组计划"},time:"2017-05-27 00:00:00"},{id:1,user:{nickname:"曲丽丽",avatar:"@image(64x64)"},project:{name:"高逼格设计天团",action:"更新",event:"六月迭代"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"@name",avatar:"@image(64x64)"},project:{name:"高逼格设计天团",action:"created",event:"六月迭代"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"曲丽丽",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},project:{name:"高逼格设计天团",action:"created",event:"六月迭代"},time:"2018-08-23 14:47:00"}])},u=function(){return(0,s.cL)([{id:1,name:"科学搬砖组",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},{id:2,name:"程序员日常",avatar:"https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png"},{id:1,name:"设计天团",avatar:"https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png"},{id:1,name:"中二少女团",avatar:"https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png"},{id:1,name:"骗你学计算机",avatar:"https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png"}])},l=function(){return(0,s.cL)([{item:"引用","个人":70,"团队":30,"部门":40},{item:"口碑","个人":60,"团队":70,"部门":40},{item:"产量","个人":50,"团队":60,"部门":40},{item:"贡献","个人":40,"团队":50,"部门":40},{item:"热度","个人":60,"团队":70,"部门":40},{item:"引用","个人":70,"团队":50,"部门":40}])};i().mock(/\/service/,"get",o),i().mock(/\/list\/search\/projects/,"get",c),i().mock(/\/workplace\/activity/,"get",d),i().mock(/\/workplace\/teams/,"get",u),i().mock(/\/workplace\/radar/,"get",l)},95158:function(e,t,n){"use strict";n.d(t,{$C:function(){return h},Db:function(){return m},Fb:function(){return d},MV:function(){return c},OT:function(){return g},RM:function(){return r},Wb:function(){return l},Xh:function(){return a},cf:function(){return i},jc:function(){return p},nd:function(){return s},o6:function(){return f},oF:function(){return b},sl:function(){return u},yG:function(){return o}});var a="Access-Token",i="sidebar_type",s="is_mobile",r="nav_theme",o="layout",c="fixed_header",d="fixed_sidebar",u="content_width",l="auto_hide_header",m="color",f="weak",p="multi_tab",h="app_language",g={Fluid:"Fluid",Fixed:"Fixed"},b={LIGHT:"light",DARK:"dark"}},96029:function(e,t,n){"use strict";n.r(t);var a=n(97139),i=n.n(a),s=n(44084),r=["Alipay","Angular","Stock Admin","Stock Admin","Bootstrap","React","Vue","Webpack"],o=["https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png","https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png","https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png","https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png","https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png"],c=["https://gw.alipayobjects.com/zos/rmsportal/uMfMFlvUuceEyPpotzlq.png","https://gw.alipayobjects.com/zos/rmsportal/iZBVOIhGJiAnhplqjvZW.png","https://gw.alipayobjects.com/zos/rmsportal/iXjVmWVHbCJAyqvDxdtx.png","https://gw.alipayobjects.com/zos/rmsportal/gLaIAoVWTtLbBWZNYEMg.png"],d=["付小小","吴加好","周星星","林东东","曲丽丽"],u="段落示意：蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。",l="在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。",m="https://ant.design",f=function(e){var t=(0,s.LT)(e);console.log("queryParameters",t),t&&!t.count&&(t.count=5);for(var n=[],a=0;a<t.count;a++){var f=a+1,p=parseInt(5*Math.random(),10);n.push({id:f,avatar:o[p],owner:d[p],content:u,star:i().mock("@integer(1, 999)"),percent:i().mock("@integer(1, 999)"),like:i().mock("@integer(1, 999)"),message:i().mock("@integer(1, 999)"),description:l,href:m,title:r[a%8],updatedAt:i().mock("@datetime"),members:[{avatar:"https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png",name:"曲丽丽",id:"member1"},{avatar:"https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png",name:"王昭君",id:"member2"},{avatar:"https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png",name:"董娜娜",id:"member3"}],activeUser:Math.ceil(1e5*Math.random())+1e5,newUser:Math.ceil(1e3*Math.random())+1e3,cover:parseInt(a/4,10)%2===0?c[a%4]:c[3-a%4]})}return(0,s.cL)(n)};i().mock(/\/list\/article/,"get",f)},98924:function(e,t,n){"use strict";n.r(t),t["default"]={"user.login.userName":"用户名","user.login.password":"密码","user.login.username.placeholder":"请输入账户","user.login.password.placeholder":"请输入密码","user.login.message-invalid-credentials":"账户或密码错误","user.login.message-invalid-verification-code":"验证码错误","user.login.tab-login-credentials":"账户密码登录","user.login.tab-login-mobile":"手机号登录","user.login.mobile.placeholder":"手机号","user.login.mobile.verification-code.placeholder":"验证码","user.login.remember-me":"自动登录","user.login.forgot-password":"忘记密码","user.login.sign-in-with":"其他登录方式","user.login.signup":"注册账户","user.login.login":"登录","user.register.register":"注册","user.register.email.placeholder":"邮箱","user.register.password.placeholder":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.password.popover-message":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.confirm-password.placeholder":"确认密码","user.register.get-verification-code":"获取验证码","user.register.sign-in":"使用已有账户登录","user.register-result.msg":"你的账户：{email} 注册成功","user.register-result.activation-email":"激活邮件已发送到你的邮箱中，邮件有效期为24小时。请及时登录邮箱，点击邮件中的链接激活帐户。","user.register-result.back-home":"返回首页","user.register-result.view-mailbox":"查看邮箱","user.email.required":"请输入邮箱地址！","user.email.wrong-format":"邮箱地址格式错误！","user.userName.required":"请输入帐户名或邮箱地址","user.password.required":"请输入密码！","user.password.twice.msg":"两次输入的密码不匹配!","user.password.strength.msg":"密码强度不够 ","user.password.strength.strong":"强度：强","user.password.strength.medium":"强度：中","user.password.strength.low":"强度：低","user.password.strength.short":"强度：太短","user.confirm-password.required":"请确认密码！","user.phone-number.required":"请输入正确的手机号","user.phone-number.wrong-format":"手机号格式错误！","user.verification-code.required":"请输入验证码！"}}},t={};function n(a){var i=t[a];if(void 0!==i)return i.exports;var s=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(s.exports,s,s.exports,n),s.loaded=!0,s.exports}n.m=e,function(){var e=[];n.O=function(t,a,i,s){if(!a){var r=1/0;for(u=0;u<e.length;u++){a=e[u][0],i=e[u][1],s=e[u][2];for(var o=!0,c=0;c<a.length;c++)(!1&s||r>=s)&&Object.keys(n.O).every((function(e){return n.O[e](a[c])}))?a.splice(c--,1):(o=!1,s<r&&(r=s));if(o){e.splice(u--,1);var d=i();void 0!==d&&(t=d)}}return t}s=s||0;for(var u=e.length;u>0&&e[u-1][2]>s;u--)e[u]=e[u-1];e[u]=[a,i,s]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,a){return n.f[a](e,t),t}),[]))}}(),function(){n.u=function(e){return"js/"+({77:"lang-en-US-result-fail",1143:"fail",1980:"lang-en-US-account",2098:"lang-en-US-account-settings",2418:"lang-en-US-global",4606:"lang-en-US-user",4729:"lang-en-US-setting",5802:"lang-en-US-result-success",5924:"lang-en-US-result",6345:"lang-en-US-dashboard",6806:"user",7254:"lang-en-US-menu",7533:"lang-en-US-form",7644:"lang-zh-CN",8376:"lang-en-US-dashboard-analysis",8438:"lang-en-US-form-basicForm"}[e]||e)+"."+{77:"8a04ff42",214:"faffb02c",460:"319ea4fe",675:"a00d4cda",966:"844445fa",1028:"c491c345",1039:"548eb9c2",1143:"84d22f6d",1200:"e04c9a46",1322:"4a6e1637",1350:"d9180914",1969:"6b6e77c1",1980:"ff0fa033",2098:"dbb6e6d9",2268:"2d911bdf",2413:"11fcabcf",2418:"ee8647d5",2889:"fde19284",2891:"********",3172:"56f3f96d",3228:"13566a82",3266:"a05967d5",3350:"73c3b6b3",4232:"3b91e26a",4308:"60be799b",4338:"d2e775ca",4464:"5a806f67",4606:"c2925917",4613:"e1c92526",4729:"d23fcddc",4821:"94843a39",5086:"ab217385",5531:"5e73899b",5753:"********",5802:"570053ef",5924:"205602af",6345:"4918cb27",6806:"c1a54396",6930:"1b2482f7",6996:"e749c2dc",7254:"f04ab7cd",7345:"e27cdddd",7465:"65767a12",7533:"58f626e3",7644:"d8999943",7670:"6de5d360",7972:"ea06865e",8376:"9764d835",8438:"8b02f63c",8512:"64646d93",8616:"9cf20ac3",8849:"8ce94599",8953:"b8cf1118",9944:"e03ecbf4"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"css/"+(6806===e?"user":e)+"."+{214:"bfe510ec",675:"0f76a824",1028:"b8817d61",1200:"75ca6d7c",1322:"9ad0d7ba",1350:"ccb9e800",1969:"f87dbea0",2268:"56f5c7a3",2413:"95983b7d",2889:"c0a8846d",3228:"9b8c5ae3",3266:"d1d32285",3350:"06bf124a",4308:"dcc190e7",4464:"0e808f83",4613:"366e033a",4821:"e9fadd2f",5086:"c17d5506",5531:"8104910c",5753:"df438062",6806:"b94fe790",6930:"15af86db",7345:"499fcc54",7670:"80ca97dc",7972:"410ea25f",8512:"47102b9a",8849:"3b10793d",8953:"4d6c81af"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="vue-antd-pro:";n.l=function(a,i,s,r){if(e[a])e[a].push(i);else{var o,c;if(void 0!==s)for(var d=document.getElementsByTagName("script"),u=0;u<d.length;u++){var l=d[u];if(l.getAttribute("src")==a||l.getAttribute("data-webpack")==t+s){o=l;break}}o||(c=!0,o=document.createElement("script"),o.charset="utf-8",o.timeout=120,n.nc&&o.setAttribute("nonce",n.nc),o.setAttribute("data-webpack",t+s),o.src=a),e[a]=[i];var m=function(t,n){o.onerror=o.onload=null,clearTimeout(f);var i=e[a];if(delete e[a],o.parentNode&&o.parentNode.removeChild(o),i&&i.forEach((function(e){return e(n)})),t)return t(n)},f=setTimeout(m.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=m.bind(null,o.onerror),o.onload=m.bind(null,o.onload),c&&document.head.appendChild(o)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){n.p=""}(),function(){if("undefined"!==typeof document){var e=function(e,t,a,i,s){var r=document.createElement("link");r.rel="stylesheet",r.type="text/css",n.nc&&(r.nonce=n.nc);var o=function(n){if(r.onerror=r.onload=null,"load"===n.type)i();else{var a=n&&n.type,o=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+o+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=a,c.request=o,r.parentNode&&r.parentNode.removeChild(r),s(c)}};return r.onerror=r.onload=o,r.href=t,a?a.parentNode.insertBefore(r,a.nextSibling):document.head.appendChild(r),r},t=function(e,t){for(var n=document.getElementsByTagName("link"),a=0;a<n.length;a++){var i=n[a],s=i.getAttribute("data-href")||i.getAttribute("href");if("stylesheet"===i.rel&&(s===e||s===t))return i}var r=document.getElementsByTagName("style");for(a=0;a<r.length;a++){i=r[a],s=i.getAttribute("data-href");if(s===e||s===t)return i}},a=function(a){return new Promise((function(i,s){var r=n.miniCssF(a),o=n.p+r;if(t(r,o))return i();e(a,o,null,i,s)}))},i={3524:0};n.f.miniCss=function(e,t){var n={214:1,675:1,1028:1,1200:1,1322:1,1350:1,1969:1,2268:1,2413:1,2889:1,3228:1,3266:1,3350:1,4308:1,4464:1,4613:1,4821:1,5086:1,5531:1,5753:1,6806:1,6930:1,7345:1,7670:1,7972:1,8512:1,8849:1,8953:1};i[e]?t.push(i[e]):0!==i[e]&&n[e]&&t.push(i[e]=a(e).then((function(){i[e]=0}),(function(t){throw delete i[e],t})))}}}(),function(){var e={3524:0};n.f.j=function(t,a){var i=n.o(e,t)?e[t]:void 0;if(0!==i)if(i)a.push(i[2]);else{var s=new Promise((function(n,a){i=e[t]=[n,a]}));a.push(i[2]=s);var r=n.p+n.u(t),o=new Error,c=function(a){if(n.o(e,t)&&(i=e[t],0!==i&&(e[t]=void 0),i)){var s=a&&("load"===a.type?"missing":a.type),r=a&&a.target&&a.target.src;o.message="Loading chunk "+t+" failed.\n("+s+": "+r+")",o.name="ChunkLoadError",o.type=s,o.request=r,i[1](o)}};n.l(r,c,"chunk-"+t,t)}},n.O.j=function(t){return 0===e[t]};var t=function(t,a){var i,s,r=a[0],o=a[1],c=a[2],d=0;if(r.some((function(t){return 0!==e[t]}))){for(i in o)n.o(o,i)&&(n.m[i]=o[i]);if(c)var u=c(n)}for(t&&t(a);d<r.length;d++)s=r[d],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(u)},a=self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=n.O(void 0,[504],(function(){return n(78287)}));a=n.O(a)})();