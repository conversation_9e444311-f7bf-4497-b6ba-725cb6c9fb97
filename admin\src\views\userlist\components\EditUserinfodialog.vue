<template>
  <div>
    <a-modal
      title="修改用户信息"
      :width="600"
      :visible="editUserdialog"
      :confirmLoading="editUserDialogloading"
      @ok="OkeditUserdialog"
      @cancel="CanceleditUserdialog">
      <a-form :form="editUserform" ref="editUserform">
        <a-form-item label="所属代理" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择所属代理" v-decorator="['agentId', {}]">
            <a-select-option v-for="(item, index) in agentlist" :key="index" :value="item.id">{{
              item.agentName
            }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="账户类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择账户类型" v-decorator="['accountType', {}]">
            <a-select-option :value="0">真实客户</a-select-option>
            <a-select-option :value="1">虚拟用户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="登录状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择登录状态" v-decorator="['isLogin', {}]">
            <a-select-option :value="0">可登录</a-select-option>
            <a-select-option :value="1">不可登录</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="交易状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择交易状态" v-decorator="['isLock', {}]">
            <a-select-option :value="0">可交易</a-select-option>
            <a-select-option :value="1">不可交易</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="提现状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择提现状态" v-decorator="['isWithdraw', {}]">
            <a-select-option :value="0">可提现</a-select-option>
            <a-select-option :value="1">不可提现</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="快捷充值" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select placeholder="请选择快捷充值状态" v-decorator="['enableRecharge', {}]">
            <a-select-option :value="0">不可充值</a-select-option>
            <a-select-option :value="1">可充值</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="手机号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入手机号" v-decorator="['phone', {}]" />
        </a-form-item>
        <a-form-item label="用户名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入用户名" v-decorator="['realName', {}]" />
        </a-form-item>
        <a-form-item label="密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入密码" v-decorator="['pwd', {}]" />
        </a-form-item>
        <a-form-item label="资金密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入密码" v-decorator="['withPwd', {}]" />
        </a-form-item>
        <a-form-item label="身份证号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入身份证号码" v-decorator="['idCard', {}]" />
        </a-form-item>
        <a-form-item label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入地址" v-decorator="['addr', {}]" />
        </a-form-item>
        <a-form-item label="身份证正面" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <div class="upload-container">
            <a-upload-dragger name="file" :show-upload-list="false" :before-upload="beforeUploadImg1" :customRequest="uploadImg1" class="custom-dragger">
              <div v-if="img1Loading" class="loading-container">
                <a-icon type="loading" class="loading-icon" />
                <p class="loading-text">上传中...</p>
              </div>
              <template v-else-if="!img1Url">
                <p class="ant-upload-drag-icon">
                  <a-icon type="idcard" />
                </p>
                <p class="ant-upload-text">点击或拖拽上传身份证正面照片</p>
                <p class="ant-upload-hint">支持JPG、PNG格式</p>
              </template>
              <div v-else class="preview-container">
                <img :src="img1Url" alt="身份证正面" class="preview-image" />
                <div class="preview-mask">
                  <p>点击重新上传</p>
                </div>
              </div>
            </a-upload-dragger>
          </div>
        </a-form-item>
        <a-form-item label="身份证反面" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <div class="upload-container">
            <a-upload-dragger name="file" :show-upload-list="false" :before-upload="beforeUploadImg2" :customRequest="uploadImg2" class="custom-dragger">
              <div v-if="img2Loading" class="loading-container">
                <a-icon type="loading" class="loading-icon" />
                <p class="loading-text">上传中...</p>
              </div>
              <template v-else-if="!img2Url">
                <p class="ant-upload-drag-icon">
                  <a-icon type="idcard" />
                </p>
                <p class="ant-upload-text">点击或拖拽上传身份证反面照片</p>
                <p class="ant-upload-hint">支持JPG、PNG格式</p>
              </template>
              <div v-else class="preview-container">
                <img :src="img2Url" alt="身份证反面" class="preview-image" />
                <div class="preview-mask">
                  <p>点击重新上传</p>
                </div>
              </div>
            </a-upload-dragger>
          </div>
        </a-form-item>
        <a-form-item label="手持身份证" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <div class="upload-container">
            <a-upload-dragger name="file" :show-upload-list="false" :before-upload="beforeUploadImg3" :customRequest="uploadImg3" class="custom-dragger">
              <div v-if="img3Loading" class="loading-container">
                <a-icon type="loading" class="loading-icon" />
                <p class="loading-text">上传中...</p>
              </div>
              <template v-else-if="!img3Url">
                <p class="ant-upload-drag-icon">
                  <a-icon type="user" />
                </p>
                <p class="ant-upload-text">点击或拖拽上传手持身份证照片</p>
                <p class="ant-upload-hint">支持JPG、PNG格式</p>
              </template>
              <div v-else class="preview-container">
                <img :src="img3Url" alt="手持身份证" class="preview-image" />
                <div class="preview-mask">
                  <p>点击重新上传</p>
                </div>
              </div>
            </a-upload-dragger>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script>
import { userupdate } from '@/api/home'
import pick from 'lodash.pick'

export default {
    components: {},
    props: {
        getinit: {
            type: Function,
            default: function () {}
        },
        agentlist: {
            type: Array,
            default: () => []
        }
    },
    data () {
        return {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 7 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 13 }
            },
            editUserform: this.$form.createForm(this),
            editUserdialog: false,
            editUserDialogloading: false,
            fields: [
                'agentId',
                'phone',
                'realName',
                'pwd',
                'idCard',
                'isLogin',
                'isLock',
                'accountType',
                'img1Key',
                'img2Key',
                'img3Key',
                'isWithdraw',
                'enableRecharge'
            ],
            currentDetails: {},
            img1Url: '',
            img2Url: '',
            img3Url: '',
            img1Loading: false,
            img2Loading: false,
            img3Loading: false
        }
    },
    mounted () {
        // 不需要获取七牛云域名，上传接口已返回完整URL
    },
    methods: {
        getEditorder (val) {
            this.currentDetails = val
            this.editUserdialog = true
            this.fields.forEach((v) => this.editUserform.getFieldDecorator(v))
            this.editUserform.setFieldsValue(pick(val, this.fields))

            // 设置图片URL
            this.img1Url = val.img1Key || ''
            this.img2Url = val.img2Key || ''
            this.img3Url = val.img3Key || ''
        },
        // 新增用户取消弹窗
        CanceleditUserdialog () {
            this.editUserdialog = false
            const form = this.$refs.editUserform.form
            form.resetFields()
            // 清空图片URL
            this.img1Url = ''
            this.img2Url = ''
            this.img3Url = ''
        },
        // 新增用户确定
        OkeditUserdialog () {
            const form = this.$refs.editUserform.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    values.id = this.currentDetails.id
                    if (values.pwd) {
                        values.userPwd = values.pwd
                    }

                    // 添加图片字段
                    values.img1Key = this.img1Url
                    values.img2Key = this.img2Url
                    values.img3Key = this.img3Url

                    this.editUserDialogloading = true
                    userupdate(values).then((res) => {
                        if (res.status === 0) {
                            this.editUserdialog = false
                            this.$message.success({ content: res.msg, duration: 2 })
                            form.resetFields()
                            this.getinit()
                        } else {
                            this.$message.error({ content: res.msg })
                        }
                        this.editUserDialogloading = false
                    })
                }
            })
        },

        // 图片上传相关方法
        beforeUploadImg1 (file) {
            const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
            if (!isJpgOrPng) {
                this.$message.error('只能上传JPG或PNG格式的图片!')
                return false
            }
            const isLt2M = file.size / 1024 / 1024 < 2
            if (!isLt2M) {
                this.$message.error('图片大小不能超过2MB!')
                return false
            }
            return true
        },

        beforeUploadImg2 (file) {
            return this.beforeUploadImg1(file)
        },

        beforeUploadImg3 (file) {
            return this.beforeUploadImg1(file)
        },

        // 上传图片到七牛云
        async uploadImg1 (options) {
            const { file } = options
            // 为文件添加标识
            const newFile = new File([file], `front_${file.name}`, { type: file.type })
            this.img1Loading = true
            try {
                const url = await this.uploadToQiniu(newFile)
                if (url) {
                    this.img1Url = url
                    this.$message.success('上传成功')
                } else {
                    this.$message.error('上传失败: 未获取到有效URL')
                }
            } catch (error) {
                console.error('上传图片错误:', error)
                this.$message.error('上传失败: ' + (error.message || '未知错误'))
            } finally {
                this.img1Loading = false
            }
        },

        async uploadImg2 (options) {
            const { file } = options
            // 为文件添加标识
            const newFile = new File([file], `back_${file.name}`, { type: file.type })
            this.img2Loading = true
            try {
                const url = await this.uploadToQiniu(newFile)
                if (url) {
                    this.img2Url = url
                    this.$message.success('上传成功')
                } else {
                    this.$message.error('上传失败: 未获取到有效URL')
                }
            } catch (error) {
                console.error('上传图片错误:', error)
                this.$message.error('上传失败: ' + (error.message || '未知错误'))
            } finally {
                this.img2Loading = false
            }
        },

        async uploadImg3 (options) {
            const { file } = options
            // 为文件添加标识
            const newFile = new File([file], `holding_${file.name}`, { type: file.type })
            this.img3Loading = true
            try {
                const url = await this.uploadToQiniu(newFile)
                if (url) {
                    this.img3Url = url
                    this.$message.success('上传成功')
                } else {
                    this.$message.error('上传失败: 未获取到有效URL')
                }
            } catch (error) {
                console.error('上传图片错误:', error)
                this.$message.error('上传失败: ' + (error.message || '未知错误'))
            } finally {
                this.img3Loading = false
            }
        },

        // 将图片转换为JPEG格式
        async convertToJPEG (file) {
            if (file.type === 'image/jpeg') {
                return file // 如果已经是JPEG格式，直接返回
            }

            // 使用canvas将其他格式（如PNG、GIF）转换为JPEG
            return new Promise((resolve, reject) => {
                const img = new Image()
                const url = URL.createObjectURL(file)

                img.onload = function () {
                    const canvas = document.createElement('canvas')
                    const ctx = canvas.getContext('2d')
                    canvas.width = img.width
                    canvas.height = img.height
                    ctx.drawImage(img, 0, 0)

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob)
                            URL.revokeObjectURL(url)
                        },
                        'image/jpeg',
                        0.92 // 默认JPEG质量为0.92
                    )
                }

                img.onerror = reject
                img.src = url
            })
        },

        // 压缩图片
        async compressImageWithCanvas (file, maxWidth, maxHeight, quality) {
            return new Promise((resolve, reject) => {
                const img = new Image()
                const url = URL.createObjectURL(file)

                img.onload = function () {
                    const canvas = document.createElement('canvas')
                    const ctx = canvas.getContext('2d')

                    let width = img.width
                    let height = img.height

                    if (width > height) {
                        if (width > maxWidth) {
                            height = Math.round((maxWidth / width) * height)
                            width = maxWidth
                        }
                    } else {
                        if (height > maxHeight) {
                            width = Math.round((maxHeight / height) * width)
                            height = maxHeight
                        }
                    }

                    canvas.width = width
                    canvas.height = height
                    ctx.drawImage(img, 0, 0, width, height)

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob)
                            URL.revokeObjectURL(url)
                        },
                        'image/jpeg',
                        quality
                    )
                }

                img.onerror = reject
                img.src = url
            })
        },

        // 使用接口上传文件到七牛云
        async uploadToQiniu (file) {
            try {
                if (!file) {
                    throw new Error('请选择文件')
                }

                // 统一转换为JPEG格式
                const jpegFile = await this.convertToJPEG(file)

                // 压缩图片
                const compressedFile = await this.compressImageWithCanvas(jpegFile, 1024, 1024, 0.7)

                // 创建FormData对象
                const formData = new FormData()
                formData.append('file', compressedFile)

                // 使用接口上传
                const res = await this.$http.post('/admin/upload/uploadToQiniu.do', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                })

                console.log('上传响应:', res)

                // 处理后端响应数据结构
                if (res.data && res.data.status === 0 && res.data.data && res.data.data.url) {
                    // 上传成功，返回文件URL
                    const fileUrl = res.data.data.url
                    console.log('上传成功，URL:', fileUrl)
                    return fileUrl
                } else if (res.status === 0 && res.data && res.data.url) {
                    // 处理后端直接返回 {status:0, msg:'上传成功', data:{url:'xxx'}} 的情况
                    const fileUrl = res.data.url
                    console.log('上传成功，URL:', fileUrl)
                    return fileUrl
                } else {
                    throw new Error(res.data?.msg || res.msg || '上传失败')
                }
            } catch (error) {
                console.error('上传到七牛云时出错:', error)
                this.$message.error('上传失败: ' + (error.message || '未知错误'))
                throw error
            }
        }
    }
}
</script>
<style scoped>
.upload-container {
    width: 100%;
    margin-bottom: 10px;
}

.custom-dragger {
    border-radius: 8px;
    overflow: hidden;
    height: 160px;
}

.custom-dragger >>> .ant-upload.ant-upload-drag {
    height: 160px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s;
}

.custom-dragger >>> .ant-upload.ant-upload-drag:hover {
    border-color: #1890ff;
}

.ant-upload-drag-icon {
    margin-bottom: 8px;
}

.ant-upload-drag-icon .anticon {
    font-size: 48px;
    color: #1890ff;
}

.ant-upload-text {
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
}

.ant-upload-hint {
    font-size: 12px;
    color: #999;
}

.preview-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.preview-image {
    max-width: 100%;
    max-height: 140px;
    border-radius: 4px;
}

.preview-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    border-radius: 4px;
}

.preview-container:hover .preview-mask {
    opacity: 1;
}

.preview-mask p {
    color: #fff;
    font-size: 14px;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.loading-icon {
    font-size: 32px;
    color: #1890ff;
    margin-bottom: 8px;
}

.loading-text {
    color: #666;
}
</style>
