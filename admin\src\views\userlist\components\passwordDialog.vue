<template>
    <div>
        <a-modal title="查看密码" :width="600" :visible="passwordDialog" :footer="false" @cancel="passwordDialog = false">
            <a-spin :spinning="loading">
                <a-descriptions bordered :column="1" v-if="passwordData">
                    <a-descriptions-item label="用户ID">
                        {{ currentDetails.id || '--' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="手机号">
                        {{ currentDetails.phone ? currentDetails.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '--'
                        }}
                    </a-descriptions-item>
                    <a-descriptions-item label="真实姓名">
                        {{ currentDetails.realName || '--' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="登录密码">
                        <span style="color: #1890ff; font-weight: bold;">{{ passwordData.loginPassword || '--' }}</span>
                    </a-descriptions-item>
                    <a-descriptions-item label="提现密码">
                        <span style="color: #52c41a; font-weight: bold;">{{ passwordData.withdrawPassword || '--'
                            }}</span>
                    </a-descriptions-item>
                </a-descriptions>
                <div v-if="!passwordData && !loading" style="text-align: center; padding: 20px;">
                    <a-empty description="暂无密码信息" />
                </div>
            </a-spin>
        </a-modal>
    </div>
</template>

<script>
import { getUserPasswords } from '@/api/home'

export default {
    name: 'PasswordDialog',
    props: {
        currentDetails: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            passwordDialog: false,
            loading: false,
            passwordData: null,
        }
    },
    watch: {
        passwordDialog(newVal) {
            if (newVal) {
                this.getPasswordData()
            } else {
                this.passwordData = null
            }
        },
    },
    methods: {
        getPasswordData() {
            if (!this.currentDetails.id) {
                this.$message.error('用户ID不存在')
                return
            }

            this.loading = true
            this.passwordData = null

            getUserPasswords({ userId: this.currentDetails.id })
                .then((res) => {
                    this.loading = false
                    if (res.status === 0) {
                        this.passwordData = res.data
                    } else {
                        this.$message.error(res.msg || '获取密码信息失败')
                    }
                })
                .catch((error) => {
                    console.error('获取密码信息错误:', error)
                    this.$message.error('获取密码信息失败')
                    this.loading = false
                })
        },
    },
}
</script>

<style scoped>
.ant-descriptions-item-label {
    font-weight: bold;
    background-color: #fafafa;
}
</style>