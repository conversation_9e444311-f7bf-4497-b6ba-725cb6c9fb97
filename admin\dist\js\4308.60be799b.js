(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[4308],{11334:function(t,e,r){"use strict";r.d(e,{FJ:function(){return m},IU:function(){return l},J5:function(){return p},Up:function(){return c},WU:function(){return u},WY:function(){return f},sE:function(){return s},xi:function(){return d},zX:function(){return g}});var a=r(16771),n=r(85070),i=r.n(n),o={getProductSetting:"/api/admin/getProductSetting.do",productupdate:"/admin/product/update.do",admingetSetting:"/api/admin/getSetting.do",setupdate:"/admin/set/update.do",admingetIndexSetting:"/api/admin/getIndexSetting.do",siteindexupdate:"/admin/site/index/update.do",admingetFuturesSetting:"/api/admin/getFuturesSetting.do",sitefuturesupdate:"/admin/site/futures/update.do",admingetSiteSpreadList:"/api/admin/getSiteSpreadList.do",adminaddSiteSpread:"/api/admin/addSiteSpread.do",adminupdateSiteSpread:"/api/admin/updateSiteSpread.do"};function s(t){return(0,a.Ay)({url:o.getProductSetting,method:"post",data:i().stringify(t)})}function d(t){return(0,a.Ay)({url:o.productupdate,method:"post",data:i().stringify(t)})}function u(t){return(0,a.Ay)({url:o.admingetSetting,method:"post",data:i().stringify(t)})}function l(t){return(0,a.Ay)({url:o.setupdate,method:"post",data:i().stringify(t)})}function c(t){return(0,a.Ay)({url:o.admingetIndexSetting,method:"post",data:i().stringify(t)})}function m(t){return(0,a.Ay)({url:o.siteindexupdate,method:"post",data:i().stringify(t)})}function f(t){return(0,a.Ay)({url:o.admingetSiteSpreadList,method:"post",data:i().stringify(t)})}function p(t){return(0,a.Ay)({url:o.adminaddSiteSpread,method:"post",data:i().stringify(t)})}function g(t){return(0,a.Ay)({url:o.adminupdateSiteSpread,method:"post",data:i().stringify(t)})}},34199:function(t,e,r){var a=1/0,n=9007199254740991,i="[object Arguments]",o="[object Function]",s="[object GeneratorFunction]",d="[object Symbol]",u="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,l="object"==typeof self&&self&&self.Object===Object&&self,c=u||l||Function("return this")();function m(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}function f(t,e){var r=-1,a=t?t.length:0,n=Array(a);while(++r<a)n[r]=e(t[r],r,t);return n}function p(t,e){var r=-1,a=e.length,n=t.length;while(++r<a)t[n+r]=e[r];return t}var g=Object.prototype,h=g.hasOwnProperty,v=g.toString,y=c.Symbol,b=g.propertyIsEnumerable,S=y?y.isConcatSpreadable:void 0,w=Math.max;function x(t,e,r,a,n){var i=-1,o=t.length;r||(r=j),n||(n=[]);while(++i<o){var s=t[i];e>0&&r(s)?e>1?x(s,e-1,r,a,n):p(n,s):a||(n[n.length]=s)}return n}function A(t,e){return t=Object(t),P(t,e,(function(e,r){return r in t}))}function P(t,e,r){var a=-1,n=e.length,i={};while(++a<n){var o=e[a],s=t[o];r(s,o)&&(i[o]=s)}return i}function U(t,e){return e=w(void 0===e?t.length-1:e,0),function(){var r=arguments,a=-1,n=w(r.length-e,0),i=Array(n);while(++a<n)i[a]=r[e+a];a=-1;var o=Array(e+1);while(++a<e)o[a]=r[a];return o[e]=i,m(t,this,o)}}function j(t){return C(t)||_(t)||!!(S&&t&&t[S])}function E(t){if("string"==typeof t||I(t))return t;var e=t+"";return"0"==e&&1/t==-a?"-0":e}function _(t){return F(t)&&h.call(t,"callee")&&(!b.call(t,"callee")||v.call(t)==i)}var C=Array.isArray;function k(t){return null!=t&&L(t.length)&&!B(t)}function F(t){return O(t)&&k(t)}function B(t){var e=N(t)?v.call(t):"";return e==o||e==s}function L(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=n}function N(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function O(t){return!!t&&"object"==typeof t}function I(t){return"symbol"==typeof t||O(t)&&v.call(t)==d}var R=U((function(t,e){return null==t?{}:A(t,f(x(e,1),E))}));t.exports=R},94308:function(t,e,r){"use strict";r.r(e),r.d(e,{default:function(){return m}});var a=function(){var t=this,e=t._self._c;return e("page-header-wrapper",[e("a-form",{ref:"addUserform",staticClass:"form",attrs:{form:t.addUserform}},[e("a-card",{staticClass:"card",attrs:{title:"指数设置",bordered:!1,loading:t.loading}},[e("span",{attrs:{slot:"extra"},slot:"extra"},[t._v('填写规则请按照括号中的示例填写，":"统一为英文中的字符，提现时间为24小时制，请填写整数')]),e("a-row",{staticClass:"form-row",attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"上午开始交易时间（例：9:30）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transAmBegin",{}],expression:"['transAmBegin', {}]"}],attrs:{placeholder:"请输入上午开始交易时间"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"上午结束交易时间（例：10:30）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transAmEnd",{}],expression:"['transAmEnd', {}]"}],attrs:{placeholder:"请输入上午结束交易时间"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下午开始交易时间（例：13:30）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transPmBegin",{}],expression:"['transPmBegin', {}]"}],attrs:{placeholder:"请输入下午开始交易时间"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下午结束交易时间（例：15:00）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["transPmEnd",{}],expression:"['transPmEnd', {}]"}],attrs:{placeholder:"请输入下午结束交易时间"}})],1)],1)],1),e("p",[t._v("指数买入比例（指数买入比例即指数买入的仓位,指数最多能够买的金额）")]),e("a-row",{staticClass:"form-row",attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:12}},[e("a-form-item",{attrs:{label:"最大买入比例（例:0.8）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["buyMaxPercent",{}],expression:"['buyMaxPercent', {}]"}],attrs:{placeholder:"请输入最大买入比例"}})],1)],1)],1),e("p",[t._v("指数强平设置 （百分比均采用小数来表示,例:0.8表示80% 强制平仓线计算规则：可用资金 + （冻结保证金 * 强制平仓比例））")]),e("a-row",{staticClass:"form-row",attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"强平比例（例:0.8）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["forceSellPercent",{}],expression:"['forceSellPercent', {}]"}],attrs:{placeholder:"请输入强平比例"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"强制平仓提醒比例（例:0.5）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["forceStopRemindRatio",{}],expression:"['forceStopRemindRatio', {}]"}],attrs:{placeholder:"请输入强制平仓提醒比例"}})],1)],1)],1),e("p",[t._v(" 指数涨跌停限制 （百分比均采用小数来表示,例:0.1表示10%）")]),e("p",[t._v("在买涨的情况下,当该指数涨幅达到涨停限制的时候,用户不能买入; 当该指数涨幅达到跌停限制的时候,用户不能卖出.")]),e("p",[t._v("在买跌的情况下,当该指数涨幅达到跌停限制的时候,用户不能买入; 当该指数涨幅达到涨停限制的时候,用户不能卖出.")]),e("a-row",{staticClass:"form-row",attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"涨停限制（例:0.1）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["riseLimit",{}],expression:"['riseLimit', {}]"}],attrs:{placeholder:"请输入涨停限制"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"跌停限制（例:0.1）"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["downLimit",{}],expression:"['downLimit', {}]"}],attrs:{placeholder:"请输入跌停限制"}})],1)],1)],1)],1)],1),e("div",{staticClass:"bottomfixed"},[e("div",{staticStyle:{float:"right"}},[e("a-button",{attrs:{type:"primary",loading:t.addUserDialogloading},on:{click:t.OkaddUserdialog}},[t._v(" 保存当前设置 ")])],1)])],1)},n=[],i=(r(27296),r(89077),r(11334)),o=r(34199),s=r.n(o),d={name:"Indexsetting",data:function(){return{addUserform:this.$form.createForm(this),loading:!1,fields:["buyMaxPercent","forceSellPercent","transAmBegin","transAmEnd","transPmBegin","transPmEnd","transAmBeginUs","transAmEndUs","transPmBeginUs","transPmEndUs","transAmBeginhk","transAmEndhk","transPmBeginhk","transPmEndhk","downLimit","riseLimit","forceStopRemindRatio"],labelCol:{xs:{span:10},sm:{span:10},md:{span:10}},wrapperCol:{xs:{span:14},sm:{span:14},md:{span:14}},addUserDialogloading:!1,details:{}}},mounted:function(){this.getdetail()},methods:{OkaddUserdialog:function(){var t=this,e=this.$refs.addUserform.form;e.validateFields((function(e,r){e||(t.addUserDialogloading=!0,t.loading=!0,r.id=t.details.id,(0,i.FJ)(r).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getdetail()):t.$message.error({content:e.msg}),t.addUserDialogloading=!1})))}))},getdetail:function(){var t=this,e=this;this.loading=!0,(0,i.Up)().then((function(r){t.details=r.data,t.fields.forEach((function(e){return t.addUserform.getFieldDecorator(e)})),t.addUserform.setFieldsValue(s()(r.data,t.fields)),setTimeout((function(){e.loading=!1}),500)}))}}},u=d,l=r(6367),c=(0,l.A)(u,a,n,!1,null,"bfe9bfe6",null),m=c.exports}}]);